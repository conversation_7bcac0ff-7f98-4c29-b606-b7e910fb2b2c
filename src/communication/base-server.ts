import { EventEmitter } from 'events';
import {
  MCPRequest,
  MCPResponse,
  MCPNotification,
  MCPError,
  MCPServerConfig,
  MCPTool,
  ToolCallRequest,
  ToolCallResult,
  CommunicationMethod
} from '../types/mcp.js';

export abstract class BaseMCPServer extends EventEmitter {
  protected config: MCPServerConfig;
  protected tools: Map<string, MCPTool> = new Map();
  protected toolHandlers: Map<string, (args: any) => Promise<ToolCallResult>> = new Map();

  constructor(config: MCPServerConfig) {
    super();
    this.config = config;
    this.setupDefaultHandlers();
  }

  // Abstract methods to be implemented by specific communication methods
  abstract start(): Promise<void>;
  abstract stop(): Promise<void>;
  abstract sendResponse(response: MCPResponse): Promise<void>;
  abstract sendNotification(notification: MCPNotification): Promise<void>;

  // Tool registration
  registerTool(tool: MCPTool, handler: (args: any) => Promise<ToolCallResult>): void {
    this.tools.set(tool.name, tool);
    this.toolHandlers.set(tool.name, handler);
  }

  // Request handling
  protected async handleRequest(request: MCPRequest): Promise<MCPResponse> {
    try {
      switch (request.method) {
        case 'initialize':
          return this.handleInitialize(request);
        case 'tools/list':
          return this.handleToolsList(request);
        case 'tools/call':
          return this.handleToolCall(request);
        default:
          return this.createErrorResponse(request.id, -32601, `Method not found: ${request.method}`);
      }
    } catch (error) {
      return this.createErrorResponse(
        request.id,
        -32603,
        'Internal error',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  // Standard MCP handlers
  private handleInitialize(request: MCPRequest): MCPResponse {
    return {
      jsonrpc: '2.0',
      id: request.id,
      result: {
        protocolVersion: '2024-11-05',
        capabilities: this.config.capabilities,
        serverInfo: {
          name: this.config.name,
          version: this.config.version
        }
      }
    };
  }

  private handleToolsList(request: MCPRequest): MCPResponse {
    const tools = Array.from(this.tools.values());
    return {
      jsonrpc: '2.0',
      id: request.id,
      result: {
        tools
      }
    };
  }

  private async handleToolCall(request: MCPRequest): Promise<MCPResponse> {
    const { name, arguments: args } = request.params as ToolCallRequest;
    
    if (!this.toolHandlers.has(name)) {
      return this.createErrorResponse(request.id, -32602, `Tool not found: ${name}`);
    }

    try {
      const handler = this.toolHandlers.get(name)!;
      const result = await handler(args);
      
      return {
        jsonrpc: '2.0',
        id: request.id,
        result
      };
    } catch (error) {
      return this.createErrorResponse(
        request.id,
        -32603,
        'Tool execution error',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  // Utility methods
  protected createErrorResponse(id: string | number, code: number, message: string, data?: any): MCPResponse {
    return {
      jsonrpc: '2.0',
      id,
      error: {
        code,
        message,
        data
      }
    };
  }

  protected setupDefaultHandlers(): void {
    this.on('error', (error) => {
      console.error('MCP Server Error:', error);
    });
  }

  // Getters
  get communicationMethod(): CommunicationMethod {
    return this.config.communicationMethod;
  }

  get serverName(): string {
    return this.config.name;
  }

  get serverVersion(): string {
    return this.config.version;
  }
}
