import * as readline from 'readline';
import { BaseMCPServer } from './base-server.js';
import { MCPRequest, MCPResponse, MCPNotification, MCPServerConfig } from '../types/mcp.js';

export class StdioMCPServer extends BaseMCPServer {
  private rl?: readline.Interface;
  private isRunning = false;

  constructor(config: MCPServerConfig) {
    super({ ...config, communicationMethod: 'stdio' });
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Server is already running');
    }

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      terminal: false
    });

    this.rl.on('line', (line) => {
      this.handleIncomingMessage(line.trim());
    });

    this.rl.on('close', () => {
      this.stop();
    });

    this.isRunning = true;
    this.emit('started');
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    if (this.rl) {
      this.rl.close();
      this.rl = undefined;
    }

    this.isRunning = false;
    this.emit('stopped');
  }

  async sendResponse(response: MCPResponse): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Server is not running');
    }

    const message = JSON.stringify(response);
    process.stdout.write(message + '\n');
  }

  async sendNotification(notification: MCPNotification): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Server is not running');
    }

    const message = JSON.stringify(notification);
    process.stdout.write(message + '\n');
  }

  private async handleIncomingMessage(message: string): Promise<void> {
    if (!message) {
      return;
    }

    try {
      const parsed = JSON.parse(message);
      
      // Handle request (has id)
      if (parsed.id !== undefined) {
        const request = parsed as MCPRequest;
        const response = await this.handleRequest(request);
        await this.sendResponse(response);
      }
      // Handle notification (no id)
      else if (parsed.method) {
        // Handle notifications if needed
        this.emit('notification', parsed as MCPNotification);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      // Send error response if we can determine the id
      try {
        const parsed = JSON.parse(message);
        if (parsed.id !== undefined) {
          const errorResponse = this.createErrorResponse(
            parsed.id,
            -32700,
            'Parse error'
          );
          await this.sendResponse(errorResponse);
        }
      } catch {
        // If we can't parse at all, just log the error
        console.error('Failed to send error response');
      }
    }
  }

  get isServerRunning(): boolean {
    return this.isRunning;
  }
}
