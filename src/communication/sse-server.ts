import express, { Request, Response } from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { Server } from 'http';
import { BaseMCPServer } from './base-server.js';
import { MCPRequest, MCPResponse, MCPNotification, MCPServerConfig } from '../types/mcp.js';

interface SSEClient {
  id: string;
  response: express.Response;
  lastPing: number;
}

export class SSEMCPServer extends BaseMCPServer {
  private app: express.Application;
  private server?: Server;
  private clients: Map<string, SSEClient> = new Map();
  private isRunning = false;
  private pingInterval?: NodeJS.Timeout;

  constructor(config: MCPServerConfig) {
    super({ ...config, communicationMethod: 'sse', port: config.port || 3000 });
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(cors({
      origin: '*',
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control']
    }));
    
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: true }));
  }

  private setupRoutes(): void {
    console.log('Setting up routes...');

    // Main SSE endpoint that Cursor expects
    this.app.get('/sse', (req: Request, res: Response) => {
      console.log('SSE connection request received at /sse');
      this.handleSSEConnection(req, res);
    });

    // Alternative SSE endpoint
    this.app.get('/events', (req: Request, res: Response) => {
      console.log('SSE connection request received at /events');
      this.handleSSEConnection(req, res);
    });

    // HTTP endpoint for sending requests to server
    this.app.post('/request', async (req: Request, res: Response) => {
      try {
        const request = req.body as MCPRequest;
        const response = await this.handleRequest(request);
        res.json(response);
      } catch (error) {
        res.status(500).json({
          jsonrpc: '2.0',
          id: req.body?.id || null,
          error: {
            code: -32603,
            message: 'Internal error',
            data: error instanceof Error ? error.message : String(error)
          }
        });
      }
    });

    // MCP endpoint for POST requests (alternative to /request)
    this.app.post('/sse', async (req: Request, res: Response) => {
      try {
        const request = req.body as MCPRequest;
        const response = await this.handleRequest(request);
        res.json(response);
      } catch (error) {
        res.status(500).json({
          jsonrpc: '2.0',
          id: req.body?.id || null,
          error: {
            code: -32603,
            message: 'Internal error',
            data: error instanceof Error ? error.message : String(error)
          }
        });
      }
    });

    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'ok',
        server: this.config.name,
        version: this.config.version,
        clients: this.clients.size
      });
    });

    // Debug: List all routes
    this.app.get('/debug/routes', (req: Request, res: Response) => {
      const routes: any[] = [];
      this.app._router.stack.forEach((middleware: any) => {
        if (middleware.route) {
          routes.push({
            path: middleware.route.path,
            methods: Object.keys(middleware.route.methods)
          });
        }
      });
      res.json({ routes });
    });

    console.log('Routes setup completed');
  }

  private handleSSEConnection(req: Request, res: Response): void {
    const clientId = req.query.clientId as string || `client_${Date.now()}_${Math.random()}`;

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    });

    // Send initial MCP server info as SSE event
    res.write(`event: server-info\n`);
    res.write(`data: ${JSON.stringify({
      name: this.config.name,
      version: this.config.version,
      capabilities: this.config.capabilities
    })}\n\n`);

    // Store client
    const client: SSEClient = {
      id: clientId,
      response: res,
      lastPing: Date.now()
    };
    this.clients.set(clientId, client);

    // Handle client disconnect
    req.on('close', () => {
      this.clients.delete(clientId);
      console.log(`Client ${clientId} disconnected`);
    });

    req.on('error', () => {
      this.clients.delete(clientId);
      console.log(`Client ${clientId} error, disconnected`);
    });

    console.log(`Client ${clientId} connected via SSE`);
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Server is already running');
    }

    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.config.port, () => {
        this.isRunning = true;
        this.startPingInterval();
        console.log(`SSE MCP Server running on port ${this.config.port}`);
        this.emit('started');
        resolve();
      });

      if (this.server) {
        this.server.on('error', (error) => {
          reject(error);
        });
      }
    });
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    return new Promise((resolve) => {
      if (this.pingInterval) {
        clearInterval(this.pingInterval);
        this.pingInterval = undefined;
      }

      // Close all SSE connections
      for (const client of this.clients.values()) {
        client.response.end();
      }
      this.clients.clear();

      if (this.server) {
        this.server.close(() => {
          this.isRunning = false;
          this.emit('stopped');
          resolve();
        });
      } else {
        this.isRunning = false;
        this.emit('stopped');
        resolve();
      }
    });
  }

  async sendResponse(response: MCPResponse): Promise<void> {
    // For SSE, responses are sent via HTTP POST endpoint
    // This method is mainly for compatibility
    console.log('Response sent via HTTP:', response);
  }

  async sendNotification(notification: MCPNotification): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Server is not running');
    }

    // Send to all connected clients as proper SSE events
    for (const client of this.clients.values()) {
      try {
        client.response.write(`event: notification\n`);
        client.response.write(`data: ${JSON.stringify(notification)}\n\n`);
      } catch (error) {
        console.error(`Error sending notification to client ${client.id}:`, error);
        this.clients.delete(client.id);
      }
    }
  }

  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      const now = Date.now();

      for (const [clientId, client] of this.clients.entries()) {
        try {
          client.response.write(`event: ping\n`);
          client.response.write(`data: ${JSON.stringify({ timestamp: now })}\n\n`);
          client.lastPing = now;
        } catch (error) {
          console.error(`Error pinging client ${clientId}:`, error);
          this.clients.delete(clientId);
        }
      }
    }, 30000); // Ping every 30 seconds
  }

  get isServerRunning(): boolean {
    return this.isRunning;
  }

  get connectedClients(): number {
    return this.clients.size;
  }
}
