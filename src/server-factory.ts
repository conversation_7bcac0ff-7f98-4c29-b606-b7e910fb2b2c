import { BaseMCPServer } from './communication/base-server.js';
import { StdioMCPServer } from './communication/stdio-server.js';
import { SSEMCPServer } from './communication/sse-server.js';
import { ExtensionPerformanceAnalyzer, ANALYZE_EXTENSION_PERFORMANCE_TOOL } from './tools/performance-analyzer-extension/index.js';
import { MCPServerConfig, CommunicationMethod } from './types/mcp.js';

export class MCPServerFactory {
  static createServer(communicationMethod: CommunicationMethod, port?: number): BaseMCPServer {
    const config: MCPServerConfig = {
      name: 'weapp-performance-optimization-mcp',
      version: '1.0.0',
      communicationMethod,
      port,
      capabilities: {
        tools: {
          listChanged: false
        }
      }
    };

    let server: BaseMCPServer;

    switch (communicationMethod) {
      case 'stdio':
        server = new StdioMCPServer(config);
        break;
      case 'sse':
        server = new SSEMCPServer(config);
        break;
      default:
        throw new Error(`Unsupported communication method: ${communicationMethod}`);
    }

    // Register the extension performance analysis tool
    const extensionAnalyzer = new ExtensionPerformanceAnalyzer();
    server.registerTool(ANALYZE_EXTENSION_PERFORMANCE_TOOL, async (args) => {
      console.log('Extension analysis args:', args);
      return extensionAnalyzer.analyzeExtensionPerformance(args);
    });

    return server;
  }

  static async startServer(communicationMethod: CommunicationMethod, port?: number): Promise<BaseMCPServer> {
    const server = MCPServerFactory.createServer(communicationMethod, port);
    
    // Set up error handling
    server.on('error', (error) => {
      console.error('Server error:', error);
    });

    server.on('started', () => {
      console.log(`MCP Server started with ${communicationMethod} communication`);
      if (communicationMethod === 'sse' && port) {
        console.log(`Server available at http://localhost:${port}`);
        console.log(`Health check: http://localhost:${port}/health`);
        console.log(`SSE endpoints: http://localhost:${port}/sse and http://localhost:${port}/events`);
        console.log(`Request endpoints: http://localhost:${port}/sse (POST) and http://localhost:${port}/request`);
      }
    });

    server.on('stopped', () => {
      console.log('MCP Server stopped');
    });

    await server.start();
    return server;
  }
}
