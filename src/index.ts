#!/usr/bin/env node

import { MCPServerFactory } from './server-factory.js';
import { CommunicationMethod } from './types/mcp.js';

interface CLIOptions {
  method: CommunicationMethod;
  port?: number;
  help?: boolean;
}

function parseArgs(): CLIOptions {
  const args = process.argv.slice(2);
  const options: CLIOptions = {
    method: 'stdio' // default
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--method':
      case '-m':
        const method = args[++i];
        if (method === 'stdio' || method === 'sse') {
          options.method = method;
        } else {
          console.error(`Invalid method: ${method}. Use 'stdio' or 'sse'.`);
          process.exit(1);
        }
        break;
        
      case '--port':
      case '-p':
        const port = parseInt(args[++i]);
        if (isNaN(port) || port < 1 || port > 65535) {
          console.error(`Invalid port: ${args[i]}. Must be a number between 1 and 65535.`);
          process.exit(1);
        }
        options.port = port;
        break;
        
      case '--help':
      case '-h':
        options.help = true;
        break;
        
      default:
        console.error(`Unknown option: ${arg}`);
        process.exit(1);
    }
  }

  return options;
}

function showHelp(): void {
  console.log(`
WeApp Performance Optimization MCP Server

Usage: weapp-performance-mcp [options]

Options:
  -m, --method <method>    Communication method: 'stdio' or 'sse' (default: stdio)
  -p, --port <port>        Port number for SSE server (default: 3000)
  -h, --help              Show this help message

Examples:
  weapp-performance-mcp                    # Start with stdio
  weapp-performance-mcp -m sse             # Start with SSE on port 3000
  weapp-performance-mcp -m sse -p 8080     # Start with SSE on port 8080

Communication Methods:
  stdio: Standard input/output communication (for direct integration)
  sse:   Server-Sent Events over HTTP (for web-based clients)

Tools Available:
  - analyze_performance_issues: Analyze WeApp code for performance issues

For more information, visit: https://github.com/your-repo/weapp-performance-optimization-mcp
`);
}

async function main(): Promise<void> {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  // Validate options
  if (options.method === 'sse' && !options.port) {
    options.port = 3000; // default port for SSE
  }

  try {
    console.log('Starting WeApp Performance Optimization MCP Server...');
    console.log(`Communication method: ${options.method}`);
    if (options.port) {
      console.log(`Port: ${options.port}`);
    }

    const server = await MCPServerFactory.startServer(options.method, options.port);

    // Handle graceful shutdown
    const shutdown = async (signal: string) => {
      console.log(`\nReceived ${signal}, shutting down gracefully...`);
      try {
        await server.stop();
        process.exit(0);
      } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));

    // Keep the process alive for stdio mode
    if (options.method === 'stdio') {
      // The stdio server will handle input/output
      // Process will stay alive until stdin is closed
    }

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the server
if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { MCPServerFactory };
export * from './types/mcp.js';
export * from './tools/performance-analyzer-extension/index.js';
