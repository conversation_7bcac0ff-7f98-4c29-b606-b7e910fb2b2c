"use strict";
exports.__esModule = true;
exports.getExtensionFiles = exports.getExtensionDetails = exports.getExtensionMaps = void 0;
var fs = require("fs");
var path = require("path");
/**
 * 根据 stage 获取优先级
 * @param stage 阶段：pre、normal、post
 * @param extensionPath 扩展路径，用于判断是否为 setup 扩展
 * @returns 优先级数值
 */
function getPriorityByStage(stage, extensionPath) {
    // 检查是否为 -setup 扩展，如果是则优先级为 100
    if (extensionPath && isSetupExtension(extensionPath)) {
        return 100;
    }
    switch (stage) {
        case 'pre':
            return 80;
        case 'normal':
            return 60;
        case 'post':
            return 0;
        default:
            return 60; // 默认为 normal
    }
}
/**
 * 判断是否为 setup 扩展
 * @param extensionPath 扩展路径
 * @returns 是否为 setup 扩展
 */
function isSetupExtension(extensionPath) {
    var extensionDirName = path.basename(extensionPath);
    return extensionDirName.includes('-setup');
}
/**
 * 获取扩展名称映射到路径和优先级
 * @param pageConfigPath 页面配置路径，例如：src/ranta-config/bizs/wsc-tee-h5-trade/buy.page.json
 * @returns 扩展名称到路径和优先级的映射对象
 */
function getExtensionMaps(pageConfigPath) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(pageConfigPath)) {
            console.warn("\u9875\u9762\u914D\u7F6E\u6587\u4EF6\u4E0D\u5B58\u5728: " + pageConfigPath);
            return {};
        }
        // 读取页面配置文件
        var pageConfigContent = fs.readFileSync(pageConfigPath, 'utf-8');
        var pageConfig = JSON.parse(pageConfigContent);
        if (!pageConfig.modules || !Array.isArray(pageConfig.modules)) {
            console.warn('页面配置中没有找到 modules 字段或 modules 不是数组');
            return {};
        }
        // 获取项目根目录（假设页面配置在 src 目录下）
        var projectRoot = findProjectRoot(pageConfigPath);
        var srcDir_1 = path.join(projectRoot, 'src');
        if (!fs.existsSync(srcDir_1)) {
            console.warn("\u6E90\u7801\u76EE\u5F55\u4E0D\u5B58\u5728: " + srcDir_1);
            return {};
        }
        // 扫描所有 ext- 开头的文件夹
        var extDirs = fs.readdirSync(srcDir_1)
            .filter(function (dir) {
            var fullPath = path.join(srcDir_1, dir);
            return fs.statSync(fullPath).isDirectory() && dir.startsWith('ext-');
        });
        // 构建扩展映射
        var extensionMap = {};
        var _loop_1 = function (extDir) {
            var extensionsDir = path.join(srcDir_1, extDir, 'extensions');
            if (!fs.existsSync(extensionsDir)) {
                return "continue";
            }
            // 扫描 extensions 目录下的所有扩展文件夹
            var extensionDirs = fs.readdirSync(extensionsDir)
                .filter(function (dir) {
                var fullPath = path.join(extensionsDir, dir);
                return fs.statSync(fullPath).isDirectory();
            });
            for (var _i = 0, extensionDirs_1 = extensionDirs; _i < extensionDirs_1.length; _i++) {
                var extensionDir = extensionDirs_1[_i];
                var extensionConfigPath = path.join(extensionsDir, extensionDir, 'extension.json');
                if (fs.existsSync(extensionConfigPath)) {
                    try {
                        var extensionConfigContent = fs.readFileSync(extensionConfigPath, 'utf-8');
                        var extensionConfig = JSON.parse(extensionConfigContent);
                        if (extensionConfig.name) {
                            // 返回绝对路径和默认优先级
                            var extensionPath = path.resolve(path.join(projectRoot, 'src', extDir, 'extensions', extensionDir));
                            extensionMap[extensionConfig.name] = {
                                path: extensionPath,
                                priority: 60,
                                stage: 'normal'
                            };
                        }
                    }
                    catch (error) {
                        console.warn("\u89E3\u6790\u6269\u5C55\u914D\u7F6E\u6587\u4EF6\u5931\u8D25: " + extensionConfigPath, error);
                    }
                }
            }
        };
        for (var _i = 0, extDirs_1 = extDirs; _i < extDirs_1.length; _i++) {
            var extDir = extDirs_1[_i];
            _loop_1(extDir);
        }
        // 过滤出页面配置中实际使用的扩展，并根据 stage 设置优先级
        var usedExtensions = {};
        for (var _a = 0, _b = pageConfig.modules; _a < _b.length; _a++) {
            var module = _b[_a];
            if (module.extensionName && extensionMap[module.extensionName]) {
                var extensionInfo = extensionMap[module.extensionName];
                var stage = module.stage || 'normal';
                var priority = getPriorityByStage(stage, extensionInfo.path);
                usedExtensions[module.extensionName] = {
                    path: extensionInfo.path,
                    priority: priority,
                    stage: stage
                };
            }
        }
        return usedExtensions;
    }
    catch (error) {
        console.error('获取扩展映射失败:', error);
        return {};
    }
}
exports.getExtensionMaps = getExtensionMaps;
/**
 * 查找项目根目录
 * @param startPath 起始路径
 * @returns 项目根目录路径
 */
function findProjectRoot(startPath) {
    var currentDir = path.dirname(path.resolve(startPath));
    while (currentDir !== path.dirname(currentDir)) {
        // 检查是否存在 package.json 或其他项目标识文件
        if (fs.existsSync(path.join(currentDir, 'package.json')) ||
            fs.existsSync(path.join(currentDir, 'src'))) {
            return currentDir;
        }
        currentDir = path.dirname(currentDir);
    }
    // 如果没找到，返回当前工作目录
    return process.cwd();
}
/**
 * 获取扩展的详细信息
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @returns 扩展的详细信息
 */
function getExtensionDetails(extensionInfo) {
    try {
        var extensionPath = typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path;
        var extensionConfigPath = path.join(extensionPath, 'extension.json');
        if (fs.existsSync(extensionConfigPath)) {
            var configContent = fs.readFileSync(extensionConfigPath, 'utf-8');
            return JSON.parse(configContent);
        }
        return null;
    }
    catch (error) {
        console.error("\u83B7\u53D6\u6269\u5C55\u8BE6\u60C5\u5931\u8D25: " + (typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path), error);
        return null;
    }
}
exports.getExtensionDetails = getExtensionDetails;
/**
 * 获取扩展目录下的所有文件
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @param extensions 文件扩展名过滤器
 * @returns 文件路径数组
 */
function getExtensionFiles(extensionInfo, extensions) {
    if (extensions === void 0) { extensions = ['.vue', '.js', '.ts', '.json']; }
    var files = [];
    var extensionPath = typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path;
    function scanDirectory(dir) {
        try {
            var items = fs.readdirSync(dir);
            for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {
                var item = items_1[_i];
                var fullPath = path.join(dir, item);
                var stat = fs.statSync(fullPath);
                if (stat.isDirectory()) {
                    // 跳过 node_modules 等目录
                    if (!item.startsWith('.') && item !== 'node_modules') {
                        scanDirectory(fullPath);
                    }
                }
                else if (stat.isFile()) {
                    var ext = path.extname(item);
                    if (extensions.length === 0 || extensions.includes(ext)) {
                        files.push(fullPath);
                    }
                }
            }
        }
        catch (error) {
            console.warn("\u626B\u63CF\u76EE\u5F55\u5931\u8D25: " + dir, error);
        }
    }
    if (fs.existsSync(extensionPath)) {
        scanDirectory(extensionPath);
    }
    return files;
}
exports.getExtensionFiles = getExtensionFiles;
