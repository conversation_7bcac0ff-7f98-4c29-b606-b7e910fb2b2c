"use strict";
exports.__esModule = true;
var page_template_json_1 = require("./page.template.json");
var extension_template_json_1 = require("./extension.template.json");
function getRantaExplanationContent() {
    return [
        "首先了解中台化的架构： 一个中台化一页面一定包含一个页面配置，配置内容如下：",
        JSON.stringify(page_template_json_1["default"], null, 2),
        "页面配置中 modules 包含了组件的引用，每个组件的引用如下：",
        JSON.stringify(page_template_json_1["default"].modules[1], null, 2),
        "其次了解组件化的架构： 每个组件都有一个index.ts入口文件，以及extension.json文件，文件内容如下：",
        JSON.stringify(extension_template_json_1["default"], null, 2),
        "每个组件的配置文件中，会包含该组件的id，name，version，bundle，widget，data，event，process，platform等信息，其中widget表示该组件提供/消费了哪些组件，data表示该组件提供/消费了哪些数据，event表示该组件提供/消费了哪些事件，process表示该组件提供/消费了哪些流程，platform表示该组件在哪些平台下可用。",
        "大部分情况，组件消费的数据、事件、流程、组件，都是有其他组件提供出来相等的字段名称。",
        "例如： 一个组件消费了另外一个组件的某个数据，则在组件的extension.json中，data字段会包含该数据的名称。",
        "但是，在某些情况下，组件消费的数据、事件、流程、组件，并不是由其他组件提供出来相等的字段名称。是需要通过页面配置中的映射关系来解决的。data只能一对一绑定，其他都可以一对多绑定，例如： ",
        JSON.stringify(page_template_json_1["default"].modules[1].bindings, null, 2),
        "然后强调一下页面配置中的 containers 定义了该页面从上而下的UI布局，contents中引用的是当前页面配置中modules中的模块id，最终会根据当前模块的extension.json中定义的widget.default来确定渲染什么组件",
        "最后强调一下IO交互的关键点： 在每个模块的widget和index.ts都可以使用ctx来获取到绑定的data、event、process，以及消费其他模块提供的组件。同时也可以使用ctx来修改data或者触发event或者调用process。",
    ];
}
exports["default"] = getRantaExplanationContent;
