"use strict";
/**
 * @description 性能优化的最佳实践
 */
exports.__esModule = true;
var bestPractice = [
    '组件内使用computed数据进行dom渲染，会导致首屏渲染延迟;',
    '大量的组件渲染会影响首屏渲染性能，建议页面初始化时减少组件渲染数量',
    '使用ctx.watch监听数据变化，需要手动注销监听，否则会导致内存泄漏，推荐使用mapData进行数据监听;',
    '使用ctx.process.define定义process，需要在destroyed生命周期中手动使用ctx.process.undef注销process，否则会导致内存泄漏，推荐使用mapProcess进行注册',
    '使用ctx.event.listen监听事件，需要在destroyed生命周期中手动使用ctx.event.remove注销监听，否则会导致内存泄漏, 推荐使用mapEvent进行注册',
    '注意在destroyed生命周期中手动注销事件监听，否则会导致内存泄漏',
    '深层的嵌套组件，会导致渲染性能下降，关注多层嵌套是否有必要',
    '大量的computed计算会导致渲染性能下降，尤其是多层依赖的数据会导致频繁触发响应式，关注是否有必要computed，推荐在数据获取完成后format中完成计算处理',
    '列表渲染时，注意设置唯一key，否则会导致渲染性能下降',
    '频繁的数据计算会导致渲染性能下降，建议使用Map进行缓存计算结果',
    '用户频繁交互的场景，使用防抖和节流来避免短时间内多次执行',
];
exports["default"] = [{ ul: bestPractice }];
