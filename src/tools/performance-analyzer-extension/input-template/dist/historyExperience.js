"use strict";
/**
 * 历史经验内容
 */
exports.__esModule = true;
var historyExperience = "\n1. \u5927\u91CF\u7684\u8BA1\u7B97\u5C5E\u6027\u4F1A\u5BFC\u81F4\u6027\u80FD\u95EE\u9898\uFF0C\u5C24\u5176\u662F\u5728\u5217\u8868\u4E2D\uFF1B\u4F18\u5316\u5EFA\u8BAE\uFF1A\u5C3D\u53EF\u80FD\u5728formatter\u4E2D\u8BA1\u7B97\u6389\uFF0C\u5728\u7EC4\u4EF6\u5185\u76F4\u63A5\u4F7F\u7528\u7ED3\u679C\u3002\n2. \u9891\u7E41\u7684DOM\u64CD\u4F5C\u4F1A\u5BFC\u81F4\u6027\u80FD\u95EE\u9898\uFF0C\u6BD4\u5982\u9891\u7E41\u7684\u6570\u91CF\u7684\u52A0\u51CF\u64CD\u4F5C\uFF1B\u4F18\u5316\u5EFA\u8BAE\uFF1A \u6309\u9700\u589E\u52A0\u9632\u6296\u8282\u6D41\u3002\n3. \u5B58\u5728\u5927\u5217\u8868\u65F6\uFF0C\u5927\u91CF\u6570\u636Ev-for\u5FAA\u73AF\u65F6\uFF0C\u4F7F\u7528key\u5E76\u4E0D\u80FD\u505A\u5230\u6309\u9700\u66F4\u65B0\uFF0C\u5C24\u5176\u662F\u590D\u6742\u7684\u7EC4\u4EF6\uFF1B\u4F18\u5316\u5EFA\u8BAE\uFF1Adiff\u7B97\u6CD5\u8BA1\u7B97\u5217\u8868\u9700\u8981\u66F4\u65B0\u54EA\u4E00\u9879\uFF0C\u901A\u8FC7 setData({'list[index]': newValue}) \u7684\u65B9\u5F0F\u53EAsetData\u9700\u8981\u66F4\u65B0\u7684\u90A3\u4E00\u9879\u3002\n4. \u9891\u7E41\u7684\u7236\u5B50\u7EC4\u4EF6\u901A\u4FE1\u4F1A\u5BFC\u81F4\u6027\u80FD\u95EE\u9898\uFF0C\u5C24\u5176\u662F\u6DF1\u5C42\u5D4C\u5957\u7684\u7236\u5B50\u7EC4\u4EF6\uFF1B\u4F18\u5316\u5EFA\u8BAE\uFF1A\u5C3D\u53EF\u80FD\u4F7F\u7528\u4E8B\u4EF6\u603B\u7EBF\u4EE3\u66FF\u7236\u5B50\u7EC4\u4EF6\u901A\u4FE1\uFF0C\u5982\u679C\u7EC4\u4EF6\u662Fwidget\u7684\u60C5\u51B5\uFF0C\u53EF\u4EE5\u4F7F\u7528store\u8FDB\u884C\u901A\u8BAF\u3002\n5. \u5927\u5217\u8868\u5B58\u5728\u4E00\u4E9B\u5F02\u6B65\u6570\u636E\u83B7\u53D6\u5E76\u66F4\u65B0\u65F6\uFF0C\u5982\u679C\u4F7F\u7528setData\u5168\u91CF\u66F4\u65B0\uFF0C\u4F1A\u5BFC\u81F4\u6027\u80FD\u95EE\u9898\uFF1B\u4F18\u5316\u5EFA\u8BAE\uFF1A\u8FD9\u79CD\u60C5\u51B5\u6700\u597D\u5728\u5B50\u7EC4\u4EF6\u7EF4\u62A4\u6570\u636E\uFF0C\u5E76\u6CE8\u518C\u4E8B\u4EF6\u76D1\u542C\uFF0C\u5F02\u6B65\u6570\u636E\u83B7\u53D6\u5B8C\u6210\u540E\u901A\u8FC7\u4E8B\u4EF6\u901A\u77E5\u5B50\u7EC4\u4EF6\uFF0C\u7531\u5B50\u7EC4\u4EF6\u8FDB\u884C\u5C40\u90E8\u66F4\u65B0\u3002\n6. \u88C5\u9970\u5668@bridge\u548C@cloud\u7B49\u76F8\u5173\u7684\u4EE3\u7801\uFF0C\u5728\u7F16\u8BD1\u8FC7\u7A0B\u4F1A\u88AB\u53BB\u6389\uFF0C\u4E0D\u4F1A\u5B58\u5728\u6027\u80FD\u95EE\u9898\uFF0C\u8BF7\u5FFD\u7565\u3002\n7. \u56FE\u7247\u61D2\u52A0\u8F7D\u5E76\u4E0D\u80FD\u63D0\u5347\u6027\u80FD\uFF0C\u5FFD\u7565\u56FE\u7247\u61D2\u52A0\u8F7D\u95EE\u9898\u3002\n8. \u5982\u679C\u4F7F\u7528mapEvent\u3001mapProcess\u6CE8\u518C\u65B9\u6CD5\uFF0C\u5219\u4E0D\u9700\u8981\u4E3B\u52A8\u6CE8\u9500\uFF0C\u56E0\u4E3A\u65B9\u6CD5\u5185\u4F1A\u81EA\u52A8\u6CE8\u9500\u3002\u4F46\u662F\u4F7F\u7528ctx.event.on\u6216\u8005ctx.process.define\u65F6\uFF0C\u9700\u8981\u6709\u5BF9\u5E94\u7684\u624B\u52A8\u6CE8\u9500\uFF0C\u5426\u5219\u4F1A\u5BFC\u81F4\u5185\u5B58\u6CC4\u6F0F\u3002\n\n";
exports["default"] = historyExperience;
