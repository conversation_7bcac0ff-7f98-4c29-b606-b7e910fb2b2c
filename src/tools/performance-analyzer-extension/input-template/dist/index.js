"use strict";
exports.__esModule = true;
exports.getPerformanceAnalyzerTemplateResult = void 0;
var getExtansionMaps_1 = require("./getExtansionMaps");
var historyExperience_1 = require("./historyExperience");
exports.getPerformanceAnalyzerTemplateResult = function (pageConfigPath) {
    var extensionMaps = getExtansionMaps_1.getExtensionMaps(pageConfigPath);
    if (!extensionMaps) {
        return null;
    }
    // 按优先级排序扩展
    var sortedExtensions = Object.entries(extensionMaps)
        .sort(function (_a, _b) {
        var a = _a[1];
        var b = _b[1];
        return b.priority - a.priority;
    })
        .filter(function (_a) {
        var info = _a[1];
        return !!info.priority && info.priority > 0;
    })
        .map(function (_a) {
        var name = _a[0], info = _a[1];
        return "\u4EE3\u7801\u8DEF\u5F84\uFF1A" + info.path + "\uFF1B(\u5206\u6790\u4F18\u5148\u7EA7: " + info.priority + ")";
    });
    var result = [];
    result.push.apply(result, sortedExtensions);
    result.push("\n    \u4F60\u662F\u4E00\u4E2A\u6027\u80FD\u4F18\u5316\u5927\u5E08\uFF0C\u4F60\u9700\u8981\u6839\u636E\u4E0A\u8FF0\u4EE3\u7801\u8DEF\u5F84\u548C\u6587\u4EF6\u5185\u5BB9\uFF0C\u5206\u6790\u51FA\u6BCF\u4E00\u4E2A\u6587\u4EF6\u53EF\u80FD\u5B58\u5728\u7684\u6027\u80FD\u95EE\u9898\uFF0C\u6CE8\u610F\uFF1A\u6587\u4EF6\u4E3B\u8981\u4E3A.ts .vue .json \u6587\u4EF6\u3002\n    \u540C\u65F6\uFF0C\u4F60\u7684\u4E00\u4E9B\u8FC7\u5F80\u7684\u7ECF\u9A8C\u5982\u4E0B\uFF1A" + historyExperience_1["default"] + "\u3002\n    \u5B8C\u6210\u5206\u6790\u540E\uFF0C\u8F93\u51FA\u4E00\u4EFDmd\u683C\u5F0F\u7684\u6027\u80FD\u62A5\u544A\uFF0C\u6027\u80FD\u62A5\u544A\u5305\u542B\u6BCF\u4E00\u9879\u6027\u80FD\u95EE\u9898\u7684\u63CF\u8FF0\u3001\u4EE3\u7801\u4F4D\u7F6E\u548C\u4FEE\u590D\u5EFA\u8BAE\u3002\n    \u683C\u5F0F\u5982\u4E0B\uFF1A\n    \u95EE\u9898\u63CF\u8FF0\uFF1A[\u95EE\u9898\u63CF\u8FF0];\n    \u4EE3\u7801\u4F4D\u7F6E\uFF1A[\u6587\u4EF6\u8DEF\u5F84:\u4EE3\u7801\u4F4D\u7F6E];\n    \u4FEE\u590D\u5EFA\u8BAE\uFF1A[\u4FEE\u590D\u5EFA\u8BAE];\n  ");
    result.push("接下来开始你的分析，输出一份性能报告。");
    return result;
};
