const json2md = require("json2md")
import { getExtensionMaps } from "./getExtansionMaps";
import historyExperience from "./historyExperience";

export const getPerformanceAnalyzerTemplateResult = (pageConfigPath: string) => {
  const extensionMaps = getExtensionMaps(pageConfigPath);
  if (!extensionMaps) {
    return null;
  }
  // 按优先级排序扩展
  const sortedExtensions = Object.entries(extensionMaps)
    .sort(([, a], [, b]) => b.priority - a.priority)
    .filter(([, info]) => !!info.priority && info.priority > 0)
    .map(([name, info]) => `代码路径：${info.path}；(分析优先级: ${info.priority})`);

  const result: string[] = [];
  result.push(...sortedExtensions);
  result.push(`
    你是一个性能优化大师，你需要根据上述代码路径和文件内容，分析出每一个文件可能存在的性能问题，注意：文件主要为.ts .vue .json 文件。
    同时，你的一些过往的经验如下：${historyExperience}。
    完成分析后，输出一份md格式的性能报告，性能报告包含每一项性能问题的描述、代码位置和修复建议。
    格式如下：
    问题描述：[问题描述];
    代码位置：[文件路径:代码位置];
    修复建议：[修复建议];
  `);
  result.push(
    "接下来开始你的分析，输出一份性能报告。"
  );
 
  return result;
};
