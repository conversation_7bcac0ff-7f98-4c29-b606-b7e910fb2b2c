import * as fs from 'fs';
import * as path from 'path';

interface PageConfig {
  modules?: Array<{
    extensionName: string;
    stage?: 'pre' | 'normal' | 'post';
    [key: string]: any;
  }>;
  [key: string]: any;
}

interface ExtensionConfig {
  name: string;
  [key: string]: any;
}

interface ExtensionInfo {
  path: string;
  priority: number;
  stage: 'pre' | 'normal' | 'post';
}

interface ExtensionMap {
  [extensionName: string]: ExtensionInfo;
}

/**
 * 根据 stage 获取优先级
 * @param stage 阶段：pre、normal、post
 * @param extensionPath 扩展路径，用于判断是否为 setup 扩展
 * @returns 优先级数值
 */
function getPriorityByStage(stage: 'pre' | 'normal' | 'post', extensionPath?: string): number {
  // 检查是否为 -setup 扩展，如果是则优先级为 100
  if (extensionPath && isSetupExtension(extensionPath)) {
    return 100;
  }

  switch (stage) {
    case 'pre':
      return 80;
    case 'normal':
      return 60;
    case 'post':
      return 0;
    default:
      return 60; // 默认为 normal
  }
}

/**
 * 判断是否为 setup 扩展
 * @param extensionPath 扩展路径
 * @returns 是否为 setup 扩展
 */
function isSetupExtension(extensionPath: string): boolean {
  const extensionDirName = path.basename(extensionPath);
  return extensionDirName.includes('-setup');
}

/**
 * 获取扩展名称映射到路径和优先级
 * @param pageConfigPath 页面配置路径，例如：src/ranta-config/bizs/wsc-tee-h5-trade/buy.page.json
 * @returns 扩展名称到路径和优先级的映射对象
 */
export function getExtensionMaps(pageConfigPath: string): ExtensionMap {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(pageConfigPath)) {
      console.warn(`页面配置文件不存在: ${pageConfigPath}`);
      return {};
    }

    // 读取页面配置文件
    const pageConfigContent = fs.readFileSync(pageConfigPath, 'utf-8');
    const pageConfig: PageConfig = JSON.parse(pageConfigContent);

    if (!pageConfig.modules || !Array.isArray(pageConfig.modules)) {
      console.warn('页面配置中没有找到 modules 字段或 modules 不是数组');
      return {};
    }

    // 获取项目根目录（假设页面配置在 src 目录下）
    const projectRoot = findProjectRoot(pageConfigPath);
    const srcDir = path.join(projectRoot, 'src');

    if (!fs.existsSync(srcDir)) {
      console.warn(`源码目录不存在: ${srcDir}`);
      return {};
    }

    // 扫描所有 ext- 开头的文件夹
    const extDirs = fs.readdirSync(srcDir)
      .filter(dir => {
        const fullPath = path.join(srcDir, dir);
        return fs.statSync(fullPath).isDirectory() && dir.startsWith('ext-');
      });

    // 构建扩展映射
    const extensionMap: ExtensionMap = {};

    for (const extDir of extDirs) {
      const extensionsDir = path.join(srcDir, extDir, 'extensions');

      if (!fs.existsSync(extensionsDir)) {
        continue;
      }

      // 扫描 extensions 目录下的所有扩展文件夹
      const extensionDirs = fs.readdirSync(extensionsDir)
        .filter(dir => {
          const fullPath = path.join(extensionsDir, dir);
          return fs.statSync(fullPath).isDirectory();
        });

      for (const extensionDir of extensionDirs) {
        const extensionConfigPath = path.join(extensionsDir, extensionDir, 'extension.json');

        if (fs.existsSync(extensionConfigPath)) {
          try {
            const extensionConfigContent = fs.readFileSync(extensionConfigPath, 'utf-8');
            const extensionConfig: ExtensionConfig = JSON.parse(extensionConfigContent);

            if (extensionConfig.name) {
              // 返回绝对路径和默认优先级
              const extensionPath = path.resolve(path.join(projectRoot, 'src', extDir, 'extensions', extensionDir));
              extensionMap[extensionConfig.name] = {
                path: extensionPath,
                priority: 60, // 默认为 normal 优先级
                stage: 'normal'
              };
            }
          } catch (error) {
            console.warn(`解析扩展配置文件失败: ${extensionConfigPath}`, error);
          }
        }
      }
    }

    // 过滤出页面配置中实际使用的扩展，并根据 stage 设置优先级
    const usedExtensions: ExtensionMap = {};
    for (const module of pageConfig.modules) {
      if (module.extensionName && extensionMap[module.extensionName]) {
        const extensionInfo = extensionMap[module.extensionName];
        const stage = module.stage || 'normal';
        const priority = getPriorityByStage(stage, extensionInfo.path);

        usedExtensions[module.extensionName] = {
          path: extensionInfo.path,
          priority: priority,
          stage: stage
        };
      }
    }

    return usedExtensions;
  } catch (error) {
    console.error('获取扩展映射失败:', error);
    return {};
  }
}

/**
 * 查找项目根目录
 * @param startPath 起始路径
 * @returns 项目根目录路径
 */
function findProjectRoot(startPath: string): string {
  let currentDir = path.dirname(path.resolve(startPath));

  while (currentDir !== path.dirname(currentDir)) {
    // 检查是否存在 package.json 或其他项目标识文件
    if (fs.existsSync(path.join(currentDir, 'package.json')) ||
        fs.existsSync(path.join(currentDir, 'src'))) {
      return currentDir;
    }
    currentDir = path.dirname(currentDir);
  }

  // 如果没找到，返回当前工作目录
  return process.cwd();
}

/**
 * 获取扩展的详细信息
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @returns 扩展的详细信息
 */
export function getExtensionDetails(extensionInfo: ExtensionInfo | string): any {
  try {
    const extensionPath = typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path;
    const extensionConfigPath = path.join(extensionPath, 'extension.json');

    if (fs.existsSync(extensionConfigPath)) {
      const configContent = fs.readFileSync(extensionConfigPath, 'utf-8');
      return JSON.parse(configContent);
    }

    return null;
  } catch (error) {
    console.error(`获取扩展详情失败: ${typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path}`, error);
    return null;
  }
}

/**
 * 获取扩展目录下的所有文件
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @param extensions 文件扩展名过滤器
 * @returns 文件路径数组
 */
export function getExtensionFiles(extensionInfo: ExtensionInfo | string, extensions: string[] = ['.vue', '.js', '.ts', '.json']): string[] {
  const files: string[] = [];
  const extensionPath = typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path;

  function scanDirectory(dir: string) {
    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          // 跳过 node_modules 等目录
          if (!item.startsWith('.') && item !== 'node_modules') {
            scanDirectory(fullPath);
          }
        } else if (stat.isFile()) {
          const ext = path.extname(item);
          if (extensions.length === 0 || extensions.includes(ext)) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      console.warn(`扫描目录失败: ${dir}`, error);
    }
  }

  if (fs.existsSync(extensionPath)) {
    scanDirectory(extensionPath);
  }

  return files;
}