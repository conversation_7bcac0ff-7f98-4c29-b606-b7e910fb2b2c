import { MCPTool, ToolCallResult } from "../../types/mcp.js";
import { getPerformanceAnalyzerTemplateResult } from "./input-template/index.js";

export const ANALYZE_EXTENSION_PERFORMANCE_TOOL: MCPTool = {
  name: "analyze_weapp_extension_performance_template",
  description:
    "你是一个小程序性能分析专家，你需要根据用户提供的页面配置文件路径，通过分析产出一份高质量的性能报告。\n",
  inputSchema: {
    type: "object",
    properties: {
      pageConfigPath: {
        type: "string",
        description: "页面配置路径",
      },
    },
    required: ["pageConfigPath"],
  },
};

export class ExtensionPerformanceAnalyzer {
  async analyzeExtensionPerformance(args: {
    fileContent: string;
    pageConfigPath: string;
    analysisType?: string;
  }): Promise<ToolCallResult> {
    try {
      const { pageConfigPath } = args;

      // 获取扩展映射
      const result = getPerformanceAnalyzerTemplateResult(pageConfigPath);
      if (!result) {
        return {
          content: [
            {
              type: "text",
              text: "未能找到页面配置文件，请检查路径是否正确。",
            },
          ],
        };
      }
      return {
        content: [
          {
            type: "text",
            text: result.join("\n"),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `分析扩展性能时发生错误: ${
              error instanceof Error ? error.message : String(error)
            }`,
          },
        ],
        isError: true,
      };
    }
  }
}
