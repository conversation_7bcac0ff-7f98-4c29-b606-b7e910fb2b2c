#!/usr/bin/env node

// Simple test script to verify the SSE MCP server works
const { spawn } = require('child_process');
const http = require('http');

async function testSSEMode() {
  console.log('Testing SSE mode...');
  
  const server = spawn('node', ['dist/index.js', '--method', 'sse', '--port', '3001'], {
    stdio: ['pipe', 'pipe', 'inherit']
  });

  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 2000));

  try {
    // Test health endpoint
    console.log('Testing health endpoint...');
    const healthResponse = await makeRequest('GET', 'http://localhost:3001/health');
    console.log('Health check:', JSON.parse(healthResponse));

    // Test tools/list request
    console.log('Testing tools/list...');
    const toolsRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };
    
    const toolsResponse = await makeRequest('POST', 'http://localhost:3001/request', JSON.stringify(toolsRequest));
    console.log('Tools list:', JSON.parse(toolsResponse));

    // Test analyze_performance_issues tool
    console.log('Testing analyze_performance_issues...');
    const analyzeRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'analyze_performance_issues',
        arguments: {
          code: `<view wx:for="{{items}}" wx:for-item="item">
  <text>{{item.name ? item.name : 'No name'}}</text>
</view>`,
          fileType: 'wxml',
          context: {
            pagePath: 'pages/list/list'
          }
        }
      }
    };

    const analyzeResponse = await makeRequest('POST', 'http://localhost:3001/request', JSON.stringify(analyzeRequest));
    console.log('Analysis result:', JSON.parse(analyzeResponse));

    console.log('\nSSE test completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    server.kill();
    process.exit(0);
  }
}

function makeRequest(method, url, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(data);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        resolve(responseData);
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(data);
    }
    req.end();
  });
}

testSSEMode().catch(console.error);
