#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPServerFactory = void 0;
const server_factory_js_1 = require("./server-factory.js");
Object.defineProperty(exports, "MCPServerFactory", { enumerable: true, get: function () { return server_factory_js_1.MCPServerFactory; } });
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {
        method: 'stdio' // default
    };
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        switch (arg) {
            case '--method':
            case '-m':
                const method = args[++i];
                if (method === 'stdio' || method === 'sse') {
                    options.method = method;
                }
                else {
                    console.error(`Invalid method: ${method}. Use 'stdio' or 'sse'.`);
                    process.exit(1);
                }
                break;
            case '--port':
            case '-p':
                const port = parseInt(args[++i]);
                if (isNaN(port) || port < 1 || port > 65535) {
                    console.error(`Invalid port: ${args[i]}. Must be a number between 1 and 65535.`);
                    process.exit(1);
                }
                options.port = port;
                break;
            case '--help':
            case '-h':
                options.help = true;
                break;
            default:
                console.error(`Unknown option: ${arg}`);
                process.exit(1);
        }
    }
    return options;
}
function showHelp() {
    console.log(`
WeApp Performance Optimization MCP Server

Usage: weapp-performance-mcp [options]

Options:
  -m, --method <method>    Communication method: 'stdio' or 'sse' (default: stdio)
  -p, --port <port>        Port number for SSE server (default: 3000)
  -h, --help              Show this help message

Examples:
  weapp-performance-mcp                    # Start with stdio
  weapp-performance-mcp -m sse             # Start with SSE on port 3000
  weapp-performance-mcp -m sse -p 8080     # Start with SSE on port 8080

Communication Methods:
  stdio: Standard input/output communication (for direct integration)
  sse:   Server-Sent Events over HTTP (for web-based clients)

Tools Available:
  - analyze_performance_issues: Analyze WeApp code for performance issues

For more information, visit: https://github.com/your-repo/weapp-performance-optimization-mcp
`);
}
async function main() {
    const options = parseArgs();
    if (options.help) {
        showHelp();
        return;
    }
    // Validate options
    if (options.method === 'sse' && !options.port) {
        options.port = 3000; // default port for SSE
    }
    try {
        console.log('Starting WeApp Performance Optimization MCP Server...');
        console.log(`Communication method: ${options.method}`);
        if (options.port) {
            console.log(`Port: ${options.port}`);
        }
        const server = await server_factory_js_1.MCPServerFactory.startServer(options.method, options.port);
        // Handle graceful shutdown
        const shutdown = async (signal) => {
            console.log(`\nReceived ${signal}, shutting down gracefully...`);
            try {
                await server.stop();
                process.exit(0);
            }
            catch (error) {
                console.error('Error during shutdown:', error);
                process.exit(1);
            }
        };
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        // Keep the process alive for stdio mode
        if (options.method === 'stdio') {
            // The stdio server will handle input/output
            // Process will stay alive until stdin is closed
        }
    }
    catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
// Start the server
if (require.main === module) {
    main().catch((error) => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
__exportStar(require("./types/mcp.js"), exports);
__exportStar(require("./tools/performance-analyzer-extension.js"), exports);
//# sourceMappingURL=index.js.map