export interface MCPRequest {
    jsonrpc: '2.0';
    id: string | number;
    method: string;
    params?: any;
}
export interface MCPResponse {
    jsonrpc: '2.0';
    id: string | number;
    result?: any;
    error?: MCPError;
}
export interface MCPError {
    code: number;
    message: string;
    data?: any;
}
export interface MCPNotification {
    jsonrpc: '2.0';
    method: string;
    params?: any;
}
export interface MCPTool {
    name: string;
    description: string;
    inputSchema: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
export interface ToolCallRequest {
    name: string;
    arguments: Record<string, any>;
}
export interface ToolCallResult {
    content: Array<{
        type: 'text';
        text: string;
    }>;
    isError?: boolean;
}
export interface ServerCapabilities {
    tools?: {
        listChanged?: boolean;
    };
    resources?: {
        subscribe?: boolean;
        listChanged?: boolean;
    };
    prompts?: {
        listChanged?: boolean;
    };
    logging?: {};
}
export type CommunicationMethod = 'stdio' | 'sse';
export interface MCPServerConfig {
    name: string;
    version: string;
    capabilities: ServerCapabilities;
    communicationMethod: CommunicationMethod;
    port?: number;
}
export interface PerformanceIssue {
    type: 'memory' | 'cpu' | 'network' | 'rendering' | 'bundle';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    location?: string;
    suggestion: string;
    impact: string;
}
export interface AnalysisResult {
    issues: PerformanceIssue[];
    summary: {
        totalIssues: number;
        criticalIssues: number;
        highIssues: number;
        mediumIssues: number;
        lowIssues: number;
    };
    recommendations: string[];
}
//# sourceMappingURL=mcp.d.ts.map