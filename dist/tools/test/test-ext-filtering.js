"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTests = runTests;
exports.createTestStructure = createTestStructure;
exports.cleanupTestStructure = cleanupTestStructure;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const getExtansionMaps_1 = require("../templates/getExtansionMaps");
/**
 * 创建测试用的目录结构和配置文件
 */
function createTestStructure() {
    const testDir = path.join(process.cwd(), 'test-temp');
    const srcDir = path.join(testDir, 'src');
    // 清理旧的测试目录
    if (fs.existsSync(testDir)) {
        fs.rmSync(testDir, { recursive: true, force: true });
    }
    // 创建目录结构
    fs.mkdirSync(testDir, { recursive: true });
    fs.mkdirSync(srcDir, { recursive: true });
    // 创建多个ext文件夹
    const extFolders = [
        'ext-core-components', // 5个扩展 - 重要
        'ext-business-logic', // 3个扩展 - 重要  
        'ext-ui-enhancements', // 2个扩展 - 重要
        'ext-legacy-support', // 1个扩展 - 不重要
        'ext-experimental' // 1个扩展 - 不重要
    ];
    const extensionCounts = [5, 3, 2, 1, 1];
    for (let i = 0; i < extFolders.length; i++) {
        const extFolder = extFolders[i];
        const count = extensionCounts[i];
        const extensionsDir = path.join(srcDir, extFolder, 'extensions');
        fs.mkdirSync(extensionsDir, { recursive: true });
        // 为每个ext文件夹创建指定数量的扩展
        for (let j = 1; j <= count; j++) {
            const extensionName = `${extFolder.replace('ext-', '')}-extension-${j}`;
            const extensionDir = path.join(extensionsDir, extensionName);
            fs.mkdirSync(extensionDir, { recursive: true });
            // 创建extension.json
            const extensionConfig = {
                name: extensionName,
                version: '1.0.0',
                description: `Test extension ${j} for ${extFolder}`
            };
            fs.writeFileSync(path.join(extensionDir, 'extension.json'), JSON.stringify(extensionConfig, null, 2));
        }
    }
    // 创建页面配置文件
    const pageConfigDir = path.join(testDir, 'src', 'ranta-config', 'bizs', 'test-app');
    fs.mkdirSync(pageConfigDir, { recursive: true });
    const pageConfig = {
        modules: [
            // ext-core-components 的扩展
            { extensionName: 'core-components-extension-1', stage: 'pre' },
            { extensionName: 'core-components-extension-2', stage: 'normal' },
            { extensionName: 'core-components-extension-3', stage: 'post' },
            { extensionName: 'core-components-extension-4', stage: 'normal' },
            { extensionName: 'core-components-extension-5', stage: 'normal' },
            // ext-business-logic 的扩展
            { extensionName: 'business-logic-extension-1', stage: 'pre' },
            { extensionName: 'business-logic-extension-2', stage: 'normal' },
            { extensionName: 'business-logic-extension-3', stage: 'post' },
            // ext-ui-enhancements 的扩展
            { extensionName: 'ui-enhancements-extension-1', stage: 'normal' },
            { extensionName: 'ui-enhancements-extension-2', stage: 'normal' },
            // ext-legacy-support 的扩展（应该被过滤）
            { extensionName: 'legacy-support-extension-1', stage: 'normal' },
            // ext-experimental 的扩展（应该被过滤）
            { extensionName: 'experimental-extension-1', stage: 'normal' }
        ]
    };
    const pageConfigPath = path.join(pageConfigDir, 'test.page.json');
    fs.writeFileSync(pageConfigPath, JSON.stringify(pageConfig, null, 2));
    return {
        testDir,
        pageConfigPath: path.relative(process.cwd(), pageConfigPath)
    };
}
/**
 * 清理测试目录
 */
function cleanupTestStructure(testDir) {
    if (fs.existsSync(testDir)) {
        fs.rmSync(testDir, { recursive: true, force: true });
    }
}
/**
 * 运行测试
 */
function runTests() {
    console.log('🧪 开始ext文件夹过滤功能测试...\n');
    const { testDir, pageConfigPath } = createTestStructure();
    try {
        // 测试1: 获取统计信息
        console.log('📊 测试1: 获取ext文件夹统计信息');
        const stats = (0, getExtansionMaps_1.getExtFolderStats)(pageConfigPath, 1); // 只保留最重要的1个文件夹
        console.log(`总扩展数量: ${stats.totalExtensions}`);
        console.log(`保留前${stats.keepTopN}个重要文件夹`);
        console.log('文件夹统计:', stats.folderCounts);
        console.log('最重要文件夹:', stats.mostImportantFolders);
        console.log('所有文件夹排序:', stats.allFoldersSorted);
        // 验证统计结果
        const expectedTotal = 12; // 5+3+2+1+1
        const expectedMostImportant = 1; // 只保留最重要的1个：ext-core-components
        console.log(`✅ 总扩展数量验证: ${stats.totalExtensions === expectedTotal ? '通过' : '失败'}`);
        console.log(`✅ 最重要文件夹数量验证: ${stats.mostImportantFolders.length === expectedMostImportant ? '通过' : '失败'}`);
        console.log(`✅ 最重要文件夹验证: ${stats.mostImportantFolders[0] === 'ext-core-components' ? '通过' : '失败'}`);
        console.log();
        // 测试2: 不过滤的扩展映射
        console.log('📊 测试2: 获取所有扩展映射（不过滤）');
        const allExtensions = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, false);
        console.log(`所有扩展数量: ${Object.keys(allExtensions).length}`);
        // 测试3: 过滤后的扩展映射（只保留最重要的1个文件夹）
        console.log('📊 测试3: 获取过滤后的扩展映射（只保留最重要的）');
        const filteredExtensions = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, true, 1);
        console.log(`过滤后扩展数量: ${Object.keys(filteredExtensions).length}`);
        const expectedFiltered = 5; // 只保留ext-core-components的5个扩展
        console.log(`✅ 过滤结果验证: ${Object.keys(filteredExtensions).length === expectedFiltered ? '通过' : '失败'}`);
        // 测试4: 验证被过滤的扩展
        const removedExtensions = Object.keys(allExtensions).filter(name => !filteredExtensions[name]);
        console.log('被过滤的扩展:', removedExtensions);
        // 应该过滤掉除了ext-core-components之外的所有扩展
        const expectedRemoved = [
            'business-logic-extension-1', 'business-logic-extension-2', 'business-logic-extension-3',
            'ui-enhancements-extension-1', 'ui-enhancements-extension-2',
            'legacy-support-extension-1', 'experimental-extension-1'
        ];
        const removedCorrect = expectedRemoved.every(name => removedExtensions.includes(name));
        console.log(`✅ 过滤内容验证: ${removedCorrect ? '通过' : '失败'}`);
        console.log();
        // 测试5: 不同keepTopN值的测试
        console.log('📊 测试5: 不同keepTopN值的过滤效果');
        const keepTop1 = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, true, 1); // 只保留最重要的1个文件夹
        const keepTop2 = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, true, 2); // 保留最重要的2个文件夹
        const keepTop3 = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, true, 3); // 保留最重要的3个文件夹
        console.log(`保留前1个文件夹: ${Object.keys(keepTop1).length} 个扩展`);
        console.log(`保留前2个文件夹: ${Object.keys(keepTop2).length} 个扩展`);
        console.log(`保留前3个文件夹: ${Object.keys(keepTop3).length} 个扩展`);
        // 验证keepTopN效果
        const keepTop1Expected = 5; // ext-core-components: 5个扩展
        const keepTop2Expected = 8; // ext-core-components(5) + ext-business-logic(3)
        const keepTop3Expected = 10; // ext-core-components(5) + ext-business-logic(3) + ext-ui-enhancements(2)
        console.log(`✅ 保留前1个验证: ${Object.keys(keepTop1).length === keepTop1Expected ? '通过' : '失败'}`);
        console.log(`✅ 保留前2个验证: ${Object.keys(keepTop2).length === keepTop2Expected ? '通过' : '失败'}`);
        console.log(`✅ 保留前3个验证: ${Object.keys(keepTop3).length === keepTop3Expected ? '通过' : '失败'}`);
        console.log();
        console.log('🎉 所有测试完成！');
    }
    catch (error) {
        console.error('❌ 测试失败:', error);
    }
    finally {
        // 清理测试目录
        cleanupTestStructure(testDir);
        console.log('🧹 测试环境已清理');
    }
}
// 如果直接运行此脚本
if (require.main === module) {
    runTests();
}
//# sourceMappingURL=test-ext-filtering.js.map