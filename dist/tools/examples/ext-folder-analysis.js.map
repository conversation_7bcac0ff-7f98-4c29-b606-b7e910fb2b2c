{"version": 3, "file": "ext-folder-analysis.js", "sourceRoot": "", "sources": ["../../../src/tools/examples/ext-folder-analysis.ts"], "names": [], "mappings": ";;AAmIS,8CAAiB;AAAE,wDAAsB;AAnIlD,oEAAoF;AAEpF;;GAEG;AACH,SAAS,iBAAiB,CAAC,cAAsB,EAAE,WAAmB,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,iBAAiB,cAAc,QAAQ,CAAC,CAAC;IAErD,kBAAkB;IAClB,MAAM,KAAK,GAAG,IAAA,oCAAiB,EAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAE1D,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,MAAM,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,qBAAqB;IACrB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IACzC,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;SACrD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,CAAC,GAAG,QAAQ,CAAC;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,KAAK,IAAI,IAAI,UAAU,KAAK,KAAK,MAAM,CAAC,CAAC;IACrE,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,eAAe;IACf,OAAO,CAAC,GAAG,CAAC,WAAW,QAAQ,iBAAiB,CAAC,CAAC;IAClD,IAAI,KAAK,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACnD,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,cAAc;IACd,MAAM,kBAAkB,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAElE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACpC,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,iBAAiB;IACjB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAE1B,aAAa;IACb,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC;IAE7D,aAAa;IACb,MAAM,kBAAkB,GAAG,IAAA,mCAAgB,EAAC,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC;IAElE,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC;IAChG,OAAO,CAAC,GAAG,CAAC,QAAQ,YAAY,MAAM,CAAC,CAAC;IAExC,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;aACjD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,CAAC,GAAG,EAAE,CAAC;IAEd,eAAe;IACf,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC1B,MAAM,kBAAkB,GAA6B,EAAE,CAAC;IAExD,KAAK,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;QAChF,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAChE,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEhD,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,kBAAkB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;QACD,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,QAAQ,UAAU,GAAG,CAAC,CAAC;QACnC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,KAAK,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,eAAyB,EAAE,WAAmB,CAAC;IAC7E,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAEvC,MAAM,WAAW,GAA2B,EAAE,CAAC;IAE/C,KAAK,MAAM,UAAU,IAAI,eAAe,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAA,oCAAiB,EAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEtD,SAAS;YACT,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjE,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;YAC3D,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,KAAK,UAAU,KAAK,KAAK,CAAC,eAAe,SAAS,KAAK,CAAC,oBAAoB,CAAC,MAAM,SAAS,CAAC,CAAC;QAC5G,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,UAAU,YAAY,KAAK,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAChC,MAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;SAClD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAEjC,KAAK,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,iBAAiB,EAAE,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,KAAK,UAAU,WAAW,eAAe,CAAC,MAAM,OAAO,CAAC,CAAC;IAClF,CAAC;AACH,CAAC;AAKD,YAAY;AACZ,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,OAAO;IACP,MAAM,iBAAiB,GAAG,sDAAsD,CAAC;IAEjF,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,IAAI,CAAC;QACH,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;IACvC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACjD,CAAC;AACH,CAAC"}