"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyzeExtFolders = analyzeExtFolders;
exports.batchAnalyzeExtFolders = batchAnalyzeExtFolders;
const getExtansionMaps_1 = require("../templates/getExtansionMaps");
/**
 * 分析ext文件夹重要性的示例脚本
 */
function analyzeExtFolders(pageConfigPath, keepTopN = 1) {
    console.log(`\n=== 分析页面配置: ${pageConfigPath} ===\n`);
    // 1. 获取ext文件夹统计信息
    const stats = (0, getExtansionMaps_1.getExtFolderStats)(pageConfigPath, keepTopN);
    console.log('📊 ext文件夹统计信息:');
    console.log(`总扩展数量: ${stats.totalExtensions}`);
    console.log(`保留策略: 只保留扩展数量最多的前${keepTopN}个文件夹`);
    console.log();
    // 2. 显示所有ext文件夹的扩展数量
    console.log('📁 所有ext文件夹扩展数量 (按重要性排序):');
    const sortedFolders = Object.entries(stats.folderCounts)
        .sort(([, a], [, b]) => b - a);
    for (let i = 0; i < sortedFolders.length; i++) {
        const [folderName, count] = sortedFolders[i];
        const isImportant = i < keepTopN;
        const indicator = isImportant ? '🔥' : '❄️';
        const rank = i + 1;
        console.log(`  ${indicator} #${rank} ${folderName}: ${count} 个扩展`);
    }
    console.log();
    // 3. 显示最重要的文件夹
    console.log(`🔥 最重要的前${keepTopN}个ext文件夹 (将被保留):`);
    if (stats.mostImportantFolders.length > 0) {
        stats.mostImportantFolders.forEach((folder, index) => {
            console.log(`  ${index + 1}. ${folder} (${stats.folderCounts[folder]} 个扩展)`);
        });
    }
    else {
        console.log('  无重要文件夹');
    }
    console.log();
    // 4. 显示不重要文件夹
    const unimportantFolders = stats.allFoldersSorted.slice(keepTopN);
    console.log('❄️ 不重要ext文件夹 (将被过滤):');
    if (unimportantFolders.length > 0) {
        unimportantFolders.forEach(folder => {
            console.log(`  - ${folder} (${stats.folderCounts[folder]} 个扩展)`);
        });
    }
    else {
        console.log('  无不重要文件夹');
    }
    console.log();
    // 5. 比较过滤前后的扩展映射
    console.log('🔍 扩展过滤对比:');
    // 获取未过滤的扩展映射
    const allExtensions = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, false);
    console.log(`过滤前: ${Object.keys(allExtensions).length} 个扩展`);
    // 获取过滤后的扩展映射
    const filteredExtensions = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath, true, keepTopN);
    console.log(`过滤后: ${Object.keys(filteredExtensions).length} 个扩展`);
    const removedCount = Object.keys(allExtensions).length - Object.keys(filteredExtensions).length;
    console.log(`过滤掉: ${removedCount} 个扩展`);
    if (removedCount > 0) {
        const removedExtensions = Object.keys(allExtensions)
            .filter(name => !filteredExtensions[name]);
        console.log('被过滤的扩展:', removedExtensions);
    }
    console.log();
    // 6. 显示保留的扩展详情
    console.log('✅ 保留的扩展详情:');
    const extensionsByFolder = {};
    for (const [extensionName, extensionInfo] of Object.entries(filteredExtensions)) {
        const match = extensionInfo.path.match(/\/src\/(ext-[^\/]+)\//);
        const folderName = match ? match[1] : 'unknown';
        if (!extensionsByFolder[folderName]) {
            extensionsByFolder[folderName] = [];
        }
        extensionsByFolder[folderName].push(extensionName);
    }
    for (const [folderName, extensions] of Object.entries(extensionsByFolder)) {
        console.log(`  📁 ${folderName}:`);
        extensions.forEach(ext => {
            const info = filteredExtensions[ext];
            console.log(`    - ${ext} (${info.stage}, 优先级: ${info.priority})`);
        });
    }
}
/**
 * 批量分析多个页面配置
 */
function batchAnalyzeExtFolders(pageConfigPaths, keepTopN = 1) {
    console.log('🚀 开始批量分析ext文件夹重要性...\n');
    const globalStats = {};
    for (const configPath of pageConfigPaths) {
        try {
            const stats = (0, getExtansionMaps_1.getExtFolderStats)(configPath, keepTopN);
            // 累计全局统计
            for (const [folder, count] of Object.entries(stats.folderCounts)) {
                globalStats[folder] = (globalStats[folder] || 0) + count;
            }
            console.log(`✅ ${configPath}: ${stats.totalExtensions} 个扩展, ${stats.mostImportantFolders.length} 个重要文件夹`);
        }
        catch (error) {
            console.error(`❌ ${configPath}: 分析失败 - ${error}`);
        }
    }
    console.log('\n📊 全局ext文件夹统计:');
    const sortedGlobalStats = Object.entries(globalStats)
        .sort(([, a], [, b]) => b - a);
    for (const [folder, totalCount] of sortedGlobalStats) {
        console.log(`  ${folder}: ${totalCount} 个扩展 (跨 ${pageConfigPaths.length} 个页面)`);
    }
}
// 如果直接运行此脚本
if (require.main === module) {
    // 示例用法
    const examplePageConfig = 'src/ranta-config/bizs/wsc-tee-h5-trade/buy.page.json';
    console.log('🔍 ext文件夹重要性分析工具');
    console.log('================================');
    try {
        analyzeExtFolders(examplePageConfig);
    }
    catch (error) {
        console.error('分析失败:', error);
        console.log('\n💡 使用提示:');
        console.log('1. 确保页面配置文件路径正确');
        console.log('2. 确保项目结构包含 src/ext-*/extensions/ 目录');
        console.log('3. 确保扩展目录包含 extension.json 配置文件');
    }
}
//# sourceMappingURL=ext-folder-analysis.js.map