import { MC<PERSON>T<PERSON>, Tool<PERSON>allR<PERSON>ult } from "../types/mcp.js";
export declare const ANALYZE_EXTENSION_PERFORMANCE_TOOL: MCPTool;
export declare class ExtensionPerformanceAnalyzer {
    analyzeExtensionPerformance(args: {
        fileContent: string;
        pageConfigPath: string;
        analysisType?: string;
    }): Promise<ToolCallResult>;
}
//# sourceMappingURL=performance-analyzer-extension.d.ts.map