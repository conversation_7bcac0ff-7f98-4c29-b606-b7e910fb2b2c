interface ExtensionInfo {
    path: string;
    name: string;
    priority: number;
    stage: 'pre' | 'normal' | 'post';
}
interface ExtensionMap {
    [extensionName: string]: ExtensionInfo;
}
/**
 * 获取扩展名称映射到路径和优先级
 * @param pageConfigPath 页面配置路径，例如：src/ranta-config/bizs/wsc-tee-h5-trade/buy.page.json
 * @param filterByImportance 是否根据ext文件夹重要性过滤扩展，默认为true
 * @param keepTopN 保留扩展数量最多的前N个文件夹，默认为1（只保留最重要的）
 * @returns 扩展名称到路径和优先级的映射对象
 */
export declare function getExtensionMaps(pageConfigPath: string, filterByImportance?: boolean, keepTopN?: number): ExtensionMap;
/**
 * 获取扩展的详细信息
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @returns 扩展的详细信息
 */
export declare function getExtensionDetails(extensionInfo: ExtensionInfo | string): any;
/**
 * 获取ext文件夹统计信息
 * @param pageConfigPath 页面配置路径
 * @param keepTopN 保留前N个重要文件夹，默认为1
 * @returns ext文件夹统计信息
 */
export declare function getExtFolderStats(pageConfigPath: string, keepTopN?: number): {
    folderCounts: Record<string, number>;
    totalExtensions: number;
    mostImportantFolders: string[];
    allFoldersSorted: string[];
    keepTopN: number;
};
/**
 * 获取扩展目录下的所有文件
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @param extensions 文件扩展名过滤器
 * @returns 文件路径数组
 */
export declare function getExtensionFiles(extensionInfo: ExtensionInfo | string, extensions?: string[]): string[];
export {};
//# sourceMappingURL=getExtansionMaps.d.ts.map