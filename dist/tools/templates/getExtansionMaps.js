"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getExtensionMaps = getExtensionMaps;
exports.getExtensionDetails = getExtensionDetails;
exports.getExtFolderStats = getExtFolderStats;
exports.getExtensionFiles = getExtensionFiles;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * 根据 stage 获取优先级
 * @param stage 阶段：pre、normal、post
 * @param extensionPath 扩展路径，用于判断是否为 setup 扩展
 * @returns 优先级数值
 */
function getPriorityByStage(stage, extensionPath) {
    // 检查是否为 -setup 扩展，如果是则优先级为 100
    if (extensionPath && isSetupExtension(extensionPath)) {
        return 100;
    }
    switch (stage) {
        case 'pre':
            return 80;
        case 'normal':
            return 60;
        case 'post':
            return 0;
        default:
            return 60; // 默认为 normal
    }
}
/**
 * 判断是否为 setup 扩展
 * @param extensionPath 扩展路径
 * @returns 是否为 setup 扩展
 */
function isSetupExtension(extensionPath) {
    const extensionDirName = path.basename(extensionPath);
    return extensionDirName.includes('-setup');
}
/**
 * 从路径中提取ext文件夹名称
 * @param extensionPath 扩展路径
 * @returns ext文件夹名称，如果不是ext路径则返回null
 */
function extractExtFolderName(extensionPath) {
    // 匹配路径中的 ext- 开头的文件夹名称
    const match = extensionPath.match(/\/src\/(ext-[^\/]+)\//);
    return match ? match[1] : null;
}
/**
 * 统计每个ext文件夹下的扩展数量
 * @param extensionMap 扩展映射对象
 * @returns ext文件夹名称到扩展数量的映射
 */
function countExtensionsByFolder(extensionMap) {
    const folderCounts = {};
    for (const extensionInfo of Object.values(extensionMap)) {
        const extFolderName = extractExtFolderName(extensionInfo.path);
        if (extFolderName) {
            folderCounts[extFolderName] = (folderCounts[extFolderName] || 0) + 1;
        }
    }
    return folderCounts;
}
/**
 * 过滤出重要的扩展信息（只保留扩展数量最多的ext文件夹）
 * @param extensionMap 扩展映射对象
 * @param keepTopN 保留扩展数量最多的前N个文件夹，默认为1（只保留最多的）
 * @returns 过滤后的扩展映射对象
 */
function filterImportantExtensions(extensionMap, keepTopN = 1) {
    const folderCounts = countExtensionsByFolder(extensionMap);
    // 按扩展数量降序排列，取前N个
    const topFolders = Object.entries(folderCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, keepTopN)
        .map(([folderName, _]) => folderName);
    const importantFolders = new Set(topFolders);
    const filteredMap = {};
    for (const [extensionName, extensionInfo] of Object.entries(extensionMap)) {
        const extFolderName = extractExtFolderName(extensionInfo.path);
        if (extFolderName && importantFolders.has(extFolderName)) {
            filteredMap[extensionName] = extensionInfo;
        }
    }
    return filteredMap;
}
/**
 * 获取扩展名称映射到路径和优先级
 * @param pageConfigPath 页面配置路径，例如：src/ranta-config/bizs/wsc-tee-h5-trade/buy.page.json
 * @param filterByImportance 是否根据ext文件夹重要性过滤扩展，默认为true
 * @param keepTopN 保留扩展数量最多的前N个文件夹，默认为1（只保留最重要的）
 * @returns 扩展名称到路径和优先级的映射对象
 */
function getExtensionMaps(pageConfigPath, filterByImportance = true, keepTopN = 1) {
    try {
        // 检查文件是否存在
        if (!fs.existsSync(pageConfigPath)) {
            console.warn(`页面配置文件不存在: ${pageConfigPath}`);
            return {};
        }
        // 读取页面配置文件
        const pageConfigContent = fs.readFileSync(pageConfigPath, 'utf-8');
        const pageConfig = JSON.parse(pageConfigContent);
        if (!pageConfig.modules || !Array.isArray(pageConfig.modules)) {
            console.warn('页面配置中没有找到 modules 字段或 modules 不是数组');
            return {};
        }
        // 获取项目根目录（假设页面配置在 src 目录下）
        const projectRoot = findProjectRoot(pageConfigPath);
        const srcDir = path.join(projectRoot, 'src');
        if (!fs.existsSync(srcDir)) {
            console.warn(`源码目录不存在: ${srcDir}`);
            return {};
        }
        // 扫描所有 ext- 开头的文件夹
        const extDirs = fs.readdirSync(srcDir)
            .filter(dir => {
            const fullPath = path.join(srcDir, dir);
            return fs.statSync(fullPath).isDirectory() && dir.startsWith('ext-');
        });
        // 构建扩展映射
        const extensionMap = {};
        for (const extDir of extDirs) {
            const extensionsDir = path.join(srcDir, extDir, 'extensions');
            if (!fs.existsSync(extensionsDir)) {
                continue;
            }
            // 扫描 extensions 目录下的所有扩展文件夹
            const extensionDirs = fs.readdirSync(extensionsDir)
                .filter(dir => {
                const fullPath = path.join(extensionsDir, dir);
                return fs.statSync(fullPath).isDirectory();
            });
            for (const extensionDir of extensionDirs) {
                const extensionConfigPath = path.join(extensionsDir, extensionDir, 'extension.json');
                if (fs.existsSync(extensionConfigPath)) {
                    try {
                        const extensionConfigContent = fs.readFileSync(extensionConfigPath, 'utf-8');
                        const extensionConfig = JSON.parse(extensionConfigContent);
                        if (extensionConfig.name) {
                            // 返回绝对路径和默认优先级
                            const extensionPath = path.resolve(path.join(projectRoot, 'src', extDir, 'extensions', extensionDir));
                            extensionMap[extensionConfig.name] = {
                                path: extensionPath,
                                name: extensionConfig.name,
                                priority: 60, // 默认为 normal 优先级
                                stage: 'normal'
                            };
                        }
                    }
                    catch (error) {
                        console.warn(`解析扩展配置文件失败: ${extensionConfigPath}`, error);
                    }
                }
            }
        }
        // 过滤出页面配置中实际使用的扩展，并根据 stage 设置优先级
        const usedExtensions = {};
        for (const module of pageConfig.modules) {
            if (module.extensionName && extensionMap[module.extensionName]) {
                const extensionInfo = extensionMap[module.extensionName];
                const stage = module.stage || 'normal';
                const priority = getPriorityByStage(stage, extensionInfo.path);
                usedExtensions[module.extensionName] = {
                    path: extensionInfo.path,
                    name: module.extensionName,
                    priority: priority,
                    stage: stage
                };
            }
        }
        // 根据重要性过滤扩展
        if (filterByImportance) {
            const filteredExtensions = filterImportantExtensions(usedExtensions, keepTopN);
            // 输出过滤信息
            const originalCount = Object.keys(usedExtensions).length;
            const filteredCount = Object.keys(filteredExtensions).length;
            const folderCounts = countExtensionsByFolder(usedExtensions);
            // 获取保留的文件夹
            const topFolders = Object.entries(folderCounts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, keepTopN)
                .map(([folderName, count]) => `${folderName}(${count}个扩展)`);
            console.log(`扩展过滤统计:`);
            console.log(`- 原始扩展数量: ${originalCount}`);
            console.log(`- 过滤后扩展数量: ${filteredCount}`);
            console.log(`- 保留前${keepTopN}个重要文件夹: ${topFolders.join(', ')}`);
            console.log(`- ext文件夹统计:`, folderCounts);
            if (filteredCount < originalCount) {
                const removedExtensions = Object.keys(usedExtensions).filter(name => !filteredExtensions[name]);
                console.log(`- 被过滤的扩展:`, removedExtensions);
            }
            return filteredExtensions;
        }
        return usedExtensions;
    }
    catch (error) {
        console.error('获取扩展映射失败:', error);
        return {};
    }
}
/**
 * 查找项目根目录
 * @param startPath 起始路径
 * @returns 项目根目录路径
 */
function findProjectRoot(startPath) {
    let currentDir = path.dirname(path.resolve(startPath));
    while (currentDir !== path.dirname(currentDir)) {
        // 检查是否存在 package.json 或其他项目标识文件
        if (fs.existsSync(path.join(currentDir, 'package.json')) ||
            fs.existsSync(path.join(currentDir, 'src'))) {
            return currentDir;
        }
        currentDir = path.dirname(currentDir);
    }
    // 如果没找到，返回当前工作目录
    return process.cwd();
}
/**
 * 获取扩展的详细信息
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @returns 扩展的详细信息
 */
function getExtensionDetails(extensionInfo) {
    try {
        const extensionPath = typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path;
        const extensionConfigPath = path.join(extensionPath, 'extension.json');
        if (fs.existsSync(extensionConfigPath)) {
            const configContent = fs.readFileSync(extensionConfigPath, 'utf-8');
            return JSON.parse(configContent);
        }
        return null;
    }
    catch (error) {
        console.error(`获取扩展详情失败: ${typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path}`, error);
        return null;
    }
}
/**
 * 获取ext文件夹统计信息
 * @param pageConfigPath 页面配置路径
 * @param keepTopN 保留前N个重要文件夹，默认为1
 * @returns ext文件夹统计信息
 */
function getExtFolderStats(pageConfigPath, keepTopN = 1) {
    const extensionMap = getExtensionMaps(pageConfigPath, false); // 不过滤，获取所有扩展
    const folderCounts = countExtensionsByFolder(extensionMap);
    const totalExtensions = Object.keys(extensionMap).length;
    // 按扩展数量降序排列所有文件夹
    const allFoldersSorted = Object.entries(folderCounts)
        .sort(([, a], [, b]) => b - a)
        .map(([folderName, _]) => folderName);
    // 获取最重要的前N个文件夹
    const mostImportantFolders = allFoldersSorted.slice(0, keepTopN);
    return {
        folderCounts,
        totalExtensions,
        mostImportantFolders,
        allFoldersSorted,
        keepTopN
    };
}
/**
 * 获取扩展目录下的所有文件
 * @param extensionInfo 扩展信息对象或扩展路径字符串
 * @param extensions 文件扩展名过滤器
 * @returns 文件路径数组
 */
function getExtensionFiles(extensionInfo, extensions = ['.vue', '.js', '.ts', '.json']) {
    const files = [];
    const extensionPath = typeof extensionInfo === 'string' ? extensionInfo : extensionInfo.path;
    function scanDirectory(dir) {
        try {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                if (stat.isDirectory()) {
                    // 跳过 node_modules 等目录
                    if (!item.startsWith('.') && item !== 'node_modules') {
                        scanDirectory(fullPath);
                    }
                }
                else if (stat.isFile()) {
                    const ext = path.extname(item);
                    if (extensions.length === 0 || extensions.includes(ext)) {
                        files.push(fullPath);
                    }
                }
            }
        }
        catch (error) {
            console.warn(`扫描目录失败: ${dir}`, error);
        }
    }
    if (fs.existsSync(extensionPath)) {
        scanDirectory(extensionPath);
    }
    return files;
}
//# sourceMappingURL=getExtansionMaps.js.map