"use strict";
/**
 * 历史经验内容
 */
Object.defineProperty(exports, "__esModule", { value: true });
const historyExperience = `
1. 大量的计算属性会导致性能问题，尤其是在列表中；优化建议：尽可能在formatter中计算掉，在组件内直接使用结果。
2. 频繁的DOM操作会导致性能问题，比如频繁的数量的加减操作；优化建议： 按需增加防抖节流。
3. 存在大列表时，大量数据v-for循环时，使用key并不能做到按需更新，尤其是复杂的组件；优化建议：diff算法计算列表需要更新哪一项，通过 setData({'list[index]': newValue}) 的方式只setData需要更新的那一项。
4. 频繁的父子组件通信会导致性能问题，尤其是深层嵌套的父子组件；优化建议：尽可能使用事件总线代替父子组件通信，如果组件是widget的情况，可以使用store进行通讯。
5. 大列表存在一些异步数据获取并更新时，如果使用setData全量更新，会导致性能问题；优化建议：这种情况最好在子组件维护数据，并注册事件监听，异步数据获取完成后通过事件通知子组件，由子组件进行局部更新。
6. 装饰器@bridge和@cloud等相关的代码，在编译过程会被去掉，不会存在性能问题，请忽略。
7. 图片懒加载并不能提升性能，忽略图片懒加载问题。
8. 如果使用mapEvent、mapProcess注册方法，则不需要主动注销，因为方法内会自动注销。但是使用ctx.event.on或者ctx.process.define时，需要有对应的手动注销，否则会导致内存泄漏。

`;
exports.default = historyExperience;
//# sourceMappingURL=historyExperience.js.map