"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPerformanceAnalyzerTemplateResult = void 0;
const getExtansionMaps_1 = require("./getExtansionMaps");
const historyExperience_1 = __importDefault(require("./historyExperience"));
const getPerformanceAnalyzerTemplateResult = (pageConfigPath) => {
    const extensionMaps = (0, getExtansionMaps_1.getExtensionMaps)(pageConfigPath);
    if (!extensionMaps) {
        return null;
    }
    // 按优先级排序扩展
    const sortedExtensions = Object.entries(extensionMaps)
        .sort(([, a], [, b]) => b.priority - a.priority)
        .filter(([, info]) => !!info.priority && info.priority > 0)
        .map(([name, info]) => `代码路径：${info.path}；(分析优先级: ${info.priority})`);
    const tasks = [];
    Object.values(extensionMaps).forEach((info) => {
        tasks.push(`### 任务描述
    分析路径： ${info.path} 的代码存在什么性能问题。
    `);
    });
    const result = [
        `## 你的角色`,
        `你是一个前端小程序性能优化专家，你有很资深的性能优化经验，比如如下经验：${historyExperience_1.default}。`,
        `同时，你的总结文档的能力也很出色，针对一些性能问题会给用户如下格式的报告：`,
        `
    问题描述：[问题描述];
    代码位置：[文件路径:代码位置];
    修复建议：[修复建议];
    `,
        `现在，用户提出了如下需求，急需你的帮助优化性能问题，已知当前项目主要以ts和vue文件为主， 用户给出了想要性能优化的任务清单如下：`,
        '## 任务列表',
    ];
    result.push(...tasks);
    result.push('接下来，你需要严格按照任务清单的任务顺序进行性能分析，最终输出一份文档，文档文件名就按当前时间命名。');
    return result;
};
exports.getPerformanceAnalyzerTemplateResult = getPerformanceAnalyzerTemplateResult;
//# sourceMappingURL=index.js.map