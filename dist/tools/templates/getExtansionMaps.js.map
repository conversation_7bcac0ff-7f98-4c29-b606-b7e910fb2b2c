{"version": 3, "file": "getExtansionMaps.js", "sourceRoot": "", "sources": ["../../../src/tools/templates/getExtansionMaps.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA,4CA8HC;AA4BD,kDAeC;AAQD,8CA0BC;AAQD,8CAkCC;AApXD,uCAAyB;AACzB,2CAA6B;AA2B7B;;;;;GAKG;AACH,SAAS,kBAAkB,CAAC,KAAgC,EAAE,aAAsB;IAClF,+BAA+B;IAC/B,IAAI,aAAa,IAAI,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;QACrD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,KAAK;YACR,OAAO,EAAE,CAAC;QACZ,KAAK,QAAQ;YACX,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,CAAC,CAAC;QACX;YACE,OAAO,EAAE,CAAC,CAAC,aAAa;IAC5B,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,aAAqB;IAC7C,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IACtD,OAAO,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7C,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,aAAqB;IACjD,uBAAuB;IACvB,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC3D,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACjC,CAAC;AAED;;;;GAIG;AACH,SAAS,uBAAuB,CAAC,YAA0B;IACzD,MAAM,YAAY,GAA2B,EAAE,CAAC;IAEhD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;QACxD,MAAM,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,YAA0B,EAAE,WAAmB,CAAC;IACjF,MAAM,YAAY,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;IAE3D,iBAAiB;IACjB,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;SAC5C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;SAC7B,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC;SAClB,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;IAExC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAE7C,MAAM,WAAW,GAAiB,EAAE,CAAC;IAErC,KAAK,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC1E,MAAM,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,aAAa,IAAI,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YACzD,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAAC,cAAsB,EAAE,qBAA8B,IAAI,EAAE,WAAmB,CAAC;IAC/G,IAAI,CAAC;QACH,WAAW;QACX,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,cAAc,cAAc,EAAE,CAAC,CAAC;YAC7C,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,WAAW;QACX,MAAM,iBAAiB,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACnE,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAE7D,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,2BAA2B;QAC3B,MAAM,WAAW,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAE7C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,YAAY,MAAM,EAAE,CAAC,CAAC;YACnC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,mBAAmB;QACnB,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC;aACnC,MAAM,CAAC,GAAG,CAAC,EAAE;YACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACxC,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEL,SAAS;QACT,MAAM,YAAY,GAAiB,EAAE,CAAC;QAEtC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAE9D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAClC,SAAS;YACX,CAAC;YAED,4BAA4B;YAC5B,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;iBAChD,MAAM,CAAC,GAAG,CAAC,EAAE;gBACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;gBAC/C,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEL,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;gBAErF,IAAI,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACvC,IAAI,CAAC;wBACH,MAAM,sBAAsB,GAAG,EAAE,CAAC,YAAY,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;wBAC7E,MAAM,eAAe,GAAoB,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAE5E,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;4BACzB,eAAe;4BACf,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;4BACtG,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG;gCACnC,IAAI,EAAE,aAAa;gCACnB,IAAI,EAAE,eAAe,CAAC,IAAI;gCAC1B,QAAQ,EAAE,EAAE,EAAE,iBAAiB;gCAC/B,KAAK,EAAE,QAAQ;6BAChB,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,IAAI,CAAC,eAAe,mBAAmB,EAAE,EAAE,KAAK,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,MAAM,cAAc,GAAiB,EAAE,CAAC;QACxC,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,MAAM,CAAC,aAAa,IAAI,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC/D,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBACzD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC;gBACvC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;gBAE/D,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG;oBACrC,IAAI,EAAE,aAAa,CAAC,IAAI;oBACxB,IAAI,EAAE,MAAM,CAAC,aAAa;oBAC1B,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,KAAK;iBACb,CAAC;YACJ,CAAC;QACH,CAAC;QAED,YAAY;QACZ,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,kBAAkB,GAAG,yBAAyB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAE/E,SAAS;YACT,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;YACzD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC;YAC7D,MAAM,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;YAE7D,WAAW;YACX,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;iBAC5C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;iBAC7B,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC;iBAClB,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU,IAAI,KAAK,MAAM,CAAC,CAAC;YAE9D,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,aAAa,aAAa,EAAE,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,cAAc,aAAa,EAAE,CAAC,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YAEzC,IAAI,aAAa,GAAG,aAAa,EAAE,CAAC;gBAClC,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAChG,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,SAAiB;IACxC,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;IAEvD,OAAO,UAAU,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/C,gCAAgC;QAChC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACpD,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;YAChD,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,iBAAiB;IACjB,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC;AACvB,CAAC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAAC,aAAqC;IACvE,IAAI,CAAC;QACH,MAAM,aAAa,GAAG,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;QAC7F,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAEvE,IAAI,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACvC,MAAM,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,aAAa,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5G,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,cAAsB,EAAE,WAAmB,CAAC;IAO5E,MAAM,YAAY,GAAG,gBAAgB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa;IAC3E,MAAM,YAAY,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;IAC3D,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;IAEzD,iBAAiB;IACjB,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;SAClD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;SAC7B,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;IAExC,eAAe;IACf,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAEjE,OAAO;QACL,YAAY;QACZ,eAAe;QACf,oBAAoB;QACpB,gBAAgB;QAChB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,aAAqC,EAAE,aAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;IAC7H,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,MAAM,aAAa,GAAG,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;IAE7F,SAAS,aAAa,CAAC,GAAW;QAChC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACtC,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACvB,sBAAsB;oBACtB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;wBACrD,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC1B,CAAC;gBACH,CAAC;qBAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;oBACzB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC/B,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;wBACxD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;QACjC,aAAa,CAAC,aAAa,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}