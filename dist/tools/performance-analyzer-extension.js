"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtensionPerformanceAnalyzer = exports.ANALYZE_EXTENSION_PERFORMANCE_TOOL = void 0;
const index_js_1 = require("./templates/index.js");
exports.ANALYZE_EXTENSION_PERFORMANCE_TOOL = {
    name: "analyze_weapp_extension_performance_template",
    description: "你是一个小程序性能分析专家，你需要根据页面配置文件分析出可以让LLM更好的理解扩展的性能问题，你需要读取页面配置文件中的模块信息，然后根据模块的路径找到对应的代码文件，最后告诉LLM怎么做。\n",
    inputSchema: {
        type: "object",
        properties: {
            pageConfigPath: {
                type: "string",
                description: "页面配置路径",
            },
        },
        required: ["pageConfigPath"],
    },
};
class ExtensionPerformanceAnalyzer {
    async analyzeExtensionPerformance(args) {
        try {
            const { pageConfigPath } = args;
            // 获取扩展映射
            const result = (0, index_js_1.getPerformanceAnalyzerTemplateResult)(pageConfigPath);
            if (!result) {
                return {
                    content: [
                        {
                            type: "text",
                            text: "未能找到扩展映射，请检查页面配置路径是否正确。",
                        },
                    ],
                };
            }
            return {
                content: [
                    {
                        type: "text",
                        text: result.join("\n"),
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `分析扩展性能时发生错误: ${error instanceof Error ? error.message : String(error)}`,
                    },
                ],
                isError: true,
            };
        }
    }
}
exports.ExtensionPerformanceAnalyzer = ExtensionPerformanceAnalyzer;
//# sourceMappingURL=performance-analyzer-extension.js.map