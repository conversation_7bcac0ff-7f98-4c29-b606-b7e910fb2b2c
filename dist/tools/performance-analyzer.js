"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceAnalyzer = exports.ANALYZE_PERFORMANCE_TOOL = void 0;
const index_js_1 = require("./templates/index.js");
exports.ANALYZE_PERFORMANCE_TOOL = {
    name: 'analyze_performance_issues',
    description: '这是一个分析小程序性能问题的工具，可以根据页面配置的内容，分析相关代码中潜在的性能问题。',
    inputSchema: {
        type: 'object',
        properties: {
            pageBizPath: {
                type: 'string',
                description: '这是一个页面配置的路径，例如： pages'
            },
            componentPath: {
                type: 'string',
                description: '这是一个组件配置的路径'
            },
            pathType: {
                type: 'string',
                description: '这是一个路径的类型，例如： page 或 component',
                enum: ['page', 'component']
            }
        },
    }
};
class PerformanceAnalyzer {
    async analyzeCode(args) {
        try {
            let report = '';
            report = (0, index_js_1.getTemplateExplanation)(args);
            report = ['你是一个小程序性能分析专家，接下来将根据你提供的路径信息，根据配置分析所有组件的代码，输出一份优化方案。', args.pageBizPath || args.componentPath].join('\n');
            return {
                content: [{
                        type: 'text',
                        text: report
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `Error analyzing code: ${error instanceof Error ? error.message : String(error)}`
                    }],
                isError: true
            };
        }
    }
}
exports.PerformanceAnalyzer = PerformanceAnalyzer;
//# sourceMappingURL=performance-analyzer.js.map