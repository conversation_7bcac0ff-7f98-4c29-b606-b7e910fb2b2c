import { BaseMCPServer } from './communication/base-server.js';
import { CommunicationMethod } from './types/mcp.js';
export declare class MCPServerFactory {
    static createServer(communicationMethod: CommunicationMethod, port?: number): BaseMCPServer;
    static startServer(communicationMethod: CommunicationMethod, port?: number): Promise<BaseMCPServer>;
}
//# sourceMappingURL=server-factory.d.ts.map