"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPServerFactory = void 0;
const stdio_server_js_1 = require("./communication/stdio-server.js");
const sse_server_js_1 = require("./communication/sse-server.js");
const performance_analyzer_extension_js_1 = require("./tools/performance-analyzer-extension.js");
class MCPServerFactory {
    static createServer(communicationMethod, port) {
        const config = {
            name: 'weapp-performance-optimization-mcp',
            version: '1.0.0',
            communicationMethod,
            port,
            capabilities: {
                tools: {
                    listChanged: false
                }
            }
        };
        let server;
        switch (communicationMethod) {
            case 'stdio':
                server = new stdio_server_js_1.StdioMCPServer(config);
                break;
            case 'sse':
                server = new sse_server_js_1.SSEMCPServer(config);
                break;
            default:
                throw new Error(`Unsupported communication method: ${communicationMethod}`);
        }
        // Register the extension performance analysis tool
        const extensionAnalyzer = new performance_analyzer_extension_js_1.ExtensionPerformanceAnalyzer();
        server.registerTool(performance_analyzer_extension_js_1.ANALYZE_EXTENSION_PERFORMANCE_TOOL, async (args) => {
            console.log('Extension analysis args:', args);
            return extensionAnalyzer.analyzeExtensionPerformance(args);
        });
        return server;
    }
    static async startServer(communicationMethod, port) {
        const server = MCPServerFactory.createServer(communicationMethod, port);
        // Set up error handling
        server.on('error', (error) => {
            console.error('Server error:', error);
        });
        server.on('started', () => {
            console.log(`MCP Server started with ${communicationMethod} communication`);
            if (communicationMethod === 'sse' && port) {
                console.log(`Server available at http://localhost:${port}`);
                console.log(`Health check: http://localhost:${port}/health`);
                console.log(`SSE endpoints: http://localhost:${port}/sse and http://localhost:${port}/events`);
                console.log(`Request endpoints: http://localhost:${port}/sse (POST) and http://localhost:${port}/request`);
            }
        });
        server.on('stopped', () => {
            console.log('MCP Server stopped');
        });
        await server.start();
        return server;
    }
}
exports.MCPServerFactory = MCPServerFactory;
//# sourceMappingURL=server-factory.js.map