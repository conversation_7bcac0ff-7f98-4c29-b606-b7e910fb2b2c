"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseMCPServer = void 0;
const events_1 = require("events");
class BaseMCPServer extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.tools = new Map();
        this.toolHandlers = new Map();
        this.config = config;
        this.setupDefaultHandlers();
    }
    // Tool registration
    registerTool(tool, handler) {
        this.tools.set(tool.name, tool);
        this.toolHandlers.set(tool.name, handler);
    }
    // Request handling
    async handleRequest(request) {
        try {
            switch (request.method) {
                case 'initialize':
                    return this.handleInitialize(request);
                case 'tools/list':
                    return this.handleToolsList(request);
                case 'tools/call':
                    return this.handleToolCall(request);
                default:
                    return this.createErrorResponse(request.id, -32601, `Method not found: ${request.method}`);
            }
        }
        catch (error) {
            return this.createErrorResponse(request.id, -32603, 'Internal error', error instanceof Error ? error.message : String(error));
        }
    }
    // Standard MCP handlers
    handleInitialize(request) {
        return {
            jsonrpc: '2.0',
            id: request.id,
            result: {
                protocolVersion: '2024-11-05',
                capabilities: this.config.capabilities,
                serverInfo: {
                    name: this.config.name,
                    version: this.config.version
                }
            }
        };
    }
    handleToolsList(request) {
        const tools = Array.from(this.tools.values());
        return {
            jsonrpc: '2.0',
            id: request.id,
            result: {
                tools
            }
        };
    }
    async handleToolCall(request) {
        const { name, arguments: args } = request.params;
        if (!this.toolHandlers.has(name)) {
            return this.createErrorResponse(request.id, -32602, `Tool not found: ${name}`);
        }
        try {
            const handler = this.toolHandlers.get(name);
            const result = await handler(args);
            return {
                jsonrpc: '2.0',
                id: request.id,
                result
            };
        }
        catch (error) {
            return this.createErrorResponse(request.id, -32603, 'Tool execution error', error instanceof Error ? error.message : String(error));
        }
    }
    // Utility methods
    createErrorResponse(id, code, message, data) {
        return {
            jsonrpc: '2.0',
            id,
            error: {
                code,
                message,
                data
            }
        };
    }
    setupDefaultHandlers() {
        this.on('error', (error) => {
            console.error('MCP Server Error:', error);
        });
    }
    // Getters
    get communicationMethod() {
        return this.config.communicationMethod;
    }
    get serverName() {
        return this.config.name;
    }
    get serverVersion() {
        return this.config.version;
    }
}
exports.BaseMCPServer = BaseMCPServer;
//# sourceMappingURL=base-server.js.map