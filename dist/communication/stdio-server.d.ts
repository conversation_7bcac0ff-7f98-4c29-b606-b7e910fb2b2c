import { BaseMCPServer } from './base-server.js';
import { MCPResponse, MCPNotification, MCPServerConfig } from '../types/mcp.js';
export declare class StdioMCPServer extends BaseMCPServer {
    private rl?;
    private isRunning;
    constructor(config: MCPServerConfig);
    start(): Promise<void>;
    stop(): Promise<void>;
    sendResponse(response: MC<PERSON><PERSON>ponse): Promise<void>;
    sendNotification(notification: MCPNotification): Promise<void>;
    private handleIncomingMessage;
    get isServerRunning(): boolean;
}
//# sourceMappingURL=stdio-server.d.ts.map