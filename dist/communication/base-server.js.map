{"version": 3, "file": "base-server.js", "sourceRoot": "", "sources": ["../../src/communication/base-server.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAatC,MAAsB,aAAc,SAAQ,qBAAY;IAKtD,YAAY,MAAuB;QACjC,KAAK,EAAE,CAAC;QAJA,UAAK,GAAyB,IAAI,GAAG,EAAE,CAAC;QACxC,iBAAY,GAAwD,IAAI,GAAG,EAAE,CAAC;QAItF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAQD,oBAAoB;IACpB,YAAY,CAAC,IAAa,EAAE,OAA+C;QACzE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,mBAAmB;IACT,KAAK,CAAC,aAAa,CAAC,OAAmB;QAC/C,IAAI,CAAC;YACH,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACxC,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBACvC,KAAK,YAAY;oBACf,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACtC;oBACE,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,qBAAqB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,mBAAmB,CAC7B,OAAO,CAAC,EAAE,EACV,CAAC,KAAK,EACN,gBAAgB,EAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACvD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,wBAAwB;IAChB,gBAAgB,CAAC,OAAmB;QAC1C,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE;gBACN,eAAe,EAAE,YAAY;gBAC7B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;gBACtC,UAAU,EAAE;oBACV,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBACtB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;iBAC7B;aACF;SACF,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,OAAmB;QACzC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9C,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,MAAM,EAAE;gBACN,KAAK;aACN;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAmB;QAC9C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,MAAyB,CAAC;QAEpE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,mBAAmB,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;YAEnC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,MAAM;aACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,mBAAmB,CAC7B,OAAO,CAAC,EAAE,EACV,CAAC,KAAK,EACN,sBAAsB,EACtB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACvD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,kBAAkB;IACR,mBAAmB,CAAC,EAAmB,EAAE,IAAY,EAAE,OAAe,EAAE,IAAU;QAC1F,OAAO;YACL,OAAO,EAAE,KAAK;YACd,EAAE;YACF,KAAK,EAAE;gBACL,IAAI;gBACJ,OAAO;gBACP,IAAI;aACL;SACF,CAAC;IACJ,CAAC;IAES,oBAAoB;QAC5B,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzB,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACV,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;IACzC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;CACF;AAlID,sCAkIC"}