"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSEMCPServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const body_parser_1 = __importDefault(require("body-parser"));
const base_server_js_1 = require("./base-server.js");
class SSEMCPServer extends base_server_js_1.BaseMCPServer {
    constructor(config) {
        super({ ...config, communicationMethod: 'sse', port: config.port || 3000 });
        this.clients = new Map();
        this.isRunning = false;
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
    }
    setupMiddleware() {
        this.app.use((0, cors_1.default)({
            origin: '*',
            methods: ['GET', 'POST', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control']
        }));
        this.app.use(body_parser_1.default.json());
        this.app.use(body_parser_1.default.urlencoded({ extended: true }));
    }
    setupRoutes() {
        console.log('Setting up routes...');
        // Main SSE endpoint that Cursor expects
        this.app.get('/sse', (req, res) => {
            console.log('SSE connection request received at /sse');
            this.handleSSEConnection(req, res);
        });
        // Alternative SSE endpoint
        this.app.get('/events', (req, res) => {
            console.log('SSE connection request received at /events');
            this.handleSSEConnection(req, res);
        });
        // HTTP endpoint for sending requests to server
        this.app.post('/request', async (req, res) => {
            try {
                const request = req.body;
                const response = await this.handleRequest(request);
                res.json(response);
            }
            catch (error) {
                res.status(500).json({
                    jsonrpc: '2.0',
                    id: req.body?.id || null,
                    error: {
                        code: -32603,
                        message: 'Internal error',
                        data: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        });
        // MCP endpoint for POST requests (alternative to /request)
        this.app.post('/sse', async (req, res) => {
            try {
                const request = req.body;
                const response = await this.handleRequest(request);
                res.json(response);
            }
            catch (error) {
                res.status(500).json({
                    jsonrpc: '2.0',
                    id: req.body?.id || null,
                    error: {
                        code: -32603,
                        message: 'Internal error',
                        data: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        });
        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                server: this.config.name,
                version: this.config.version,
                clients: this.clients.size
            });
        });
        // Debug: List all routes
        this.app.get('/debug/routes', (req, res) => {
            const routes = [];
            this.app._router.stack.forEach((middleware) => {
                if (middleware.route) {
                    routes.push({
                        path: middleware.route.path,
                        methods: Object.keys(middleware.route.methods)
                    });
                }
            });
            res.json({ routes });
        });
        console.log('Routes setup completed');
    }
    handleSSEConnection(req, res) {
        const clientId = req.query.clientId || `client_${Date.now()}_${Math.random()}`;
        // Set SSE headers
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
        });
        // Send initial MCP server info as SSE event
        res.write(`event: server-info\n`);
        res.write(`data: ${JSON.stringify({
            name: this.config.name,
            version: this.config.version,
            capabilities: this.config.capabilities
        })}\n\n`);
        // Store client
        const client = {
            id: clientId,
            response: res,
            lastPing: Date.now()
        };
        this.clients.set(clientId, client);
        // Handle client disconnect
        req.on('close', () => {
            this.clients.delete(clientId);
            console.log(`Client ${clientId} disconnected`);
        });
        req.on('error', () => {
            this.clients.delete(clientId);
            console.log(`Client ${clientId} error, disconnected`);
        });
        console.log(`Client ${clientId} connected via SSE`);
    }
    async start() {
        if (this.isRunning) {
            throw new Error('Server is already running');
        }
        return new Promise((resolve, reject) => {
            this.server = this.app.listen(this.config.port, () => {
                this.isRunning = true;
                this.startPingInterval();
                console.log(`SSE MCP Server running on port ${this.config.port}`);
                this.emit('started');
                resolve();
            });
            if (this.server) {
                this.server.on('error', (error) => {
                    reject(error);
                });
            }
        });
    }
    async stop() {
        if (!this.isRunning) {
            return;
        }
        return new Promise((resolve) => {
            if (this.pingInterval) {
                clearInterval(this.pingInterval);
                this.pingInterval = undefined;
            }
            // Close all SSE connections
            for (const client of this.clients.values()) {
                client.response.end();
            }
            this.clients.clear();
            if (this.server) {
                this.server.close(() => {
                    this.isRunning = false;
                    this.emit('stopped');
                    resolve();
                });
            }
            else {
                this.isRunning = false;
                this.emit('stopped');
                resolve();
            }
        });
    }
    async sendResponse(response) {
        // For SSE, responses are sent via HTTP POST endpoint
        // This method is mainly for compatibility
        console.log('Response sent via HTTP:', response);
    }
    async sendNotification(notification) {
        if (!this.isRunning) {
            throw new Error('Server is not running');
        }
        // Send to all connected clients as proper SSE events
        for (const client of this.clients.values()) {
            try {
                client.response.write(`event: notification\n`);
                client.response.write(`data: ${JSON.stringify(notification)}\n\n`);
            }
            catch (error) {
                console.error(`Error sending notification to client ${client.id}:`, error);
                this.clients.delete(client.id);
            }
        }
    }
    startPingInterval() {
        this.pingInterval = setInterval(() => {
            const now = Date.now();
            for (const [clientId, client] of this.clients.entries()) {
                try {
                    client.response.write(`event: ping\n`);
                    client.response.write(`data: ${JSON.stringify({ timestamp: now })}\n\n`);
                    client.lastPing = now;
                }
                catch (error) {
                    console.error(`Error pinging client ${clientId}:`, error);
                    this.clients.delete(clientId);
                }
            }
        }, 30000); // Ping every 30 seconds
    }
    get isServerRunning() {
        return this.isRunning;
    }
    get connectedClients() {
        return this.clients.size;
    }
}
exports.SSEMCPServer = SSEMCPServer;
//# sourceMappingURL=sse-server.js.map