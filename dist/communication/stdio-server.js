"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StdioMCPServer = void 0;
const readline = __importStar(require("readline"));
const base_server_js_1 = require("./base-server.js");
class StdioMCPServer extends base_server_js_1.BaseMCPServer {
    constructor(config) {
        super({ ...config, communicationMethod: 'stdio' });
        this.isRunning = false;
    }
    async start() {
        if (this.isRunning) {
            throw new Error('Server is already running');
        }
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            terminal: false
        });
        this.rl.on('line', (line) => {
            this.handleIncomingMessage(line.trim());
        });
        this.rl.on('close', () => {
            this.stop();
        });
        this.isRunning = true;
        this.emit('started');
    }
    async stop() {
        if (!this.isRunning) {
            return;
        }
        if (this.rl) {
            this.rl.close();
            this.rl = undefined;
        }
        this.isRunning = false;
        this.emit('stopped');
    }
    async sendResponse(response) {
        if (!this.isRunning) {
            throw new Error('Server is not running');
        }
        const message = JSON.stringify(response);
        process.stdout.write(message + '\n');
    }
    async sendNotification(notification) {
        if (!this.isRunning) {
            throw new Error('Server is not running');
        }
        const message = JSON.stringify(notification);
        process.stdout.write(message + '\n');
    }
    async handleIncomingMessage(message) {
        if (!message) {
            return;
        }
        try {
            const parsed = JSON.parse(message);
            // Handle request (has id)
            if (parsed.id !== undefined) {
                const request = parsed;
                const response = await this.handleRequest(request);
                await this.sendResponse(response);
            }
            // Handle notification (no id)
            else if (parsed.method) {
                // Handle notifications if needed
                this.emit('notification', parsed);
            }
        }
        catch (error) {
            console.error('Error parsing message:', error);
            // Send error response if we can determine the id
            try {
                const parsed = JSON.parse(message);
                if (parsed.id !== undefined) {
                    const errorResponse = this.createErrorResponse(parsed.id, -32700, 'Parse error');
                    await this.sendResponse(errorResponse);
                }
            }
            catch {
                // If we can't parse at all, just log the error
                console.error('Failed to send error response');
            }
        }
    }
    get isServerRunning() {
        return this.isRunning;
    }
}
exports.StdioMCPServer = StdioMCPServer;
//# sourceMappingURL=stdio-server.js.map