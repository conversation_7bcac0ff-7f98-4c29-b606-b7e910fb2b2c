import { EventEmitter } from 'events';
import { MCPRequest, MCPResponse, MCPNotification, MCPServerConfig, MCPTool, ToolCallResult, CommunicationMethod } from '../types/mcp.js';
export declare abstract class BaseMCPServer extends EventEmitter {
    protected config: MCPServerConfig;
    protected tools: Map<string, MCPTool>;
    protected toolHandlers: Map<string, (args: any) => Promise<ToolCallResult>>;
    constructor(config: MCPServerConfig);
    abstract start(): Promise<void>;
    abstract stop(): Promise<void>;
    abstract sendResponse(response: MCPResponse): Promise<void>;
    abstract sendNotification(notification: MCPNotification): Promise<void>;
    registerTool(tool: MCPTool, handler: (args: any) => Promise<ToolCallResult>): void;
    protected handleRequest(request: MCPRequest): Promise<MCPResponse>;
    private handleInitialize;
    private handleToolsList;
    private handleToolCall;
    protected createErrorResponse(id: string | number, code: number, message: string, data?: any): MCPResponse;
    protected setupDefaultHandlers(): void;
    get communicationMethod(): CommunicationMethod;
    get serverName(): string;
    get serverVersion(): string;
}
//# sourceMappingURL=base-server.d.ts.map