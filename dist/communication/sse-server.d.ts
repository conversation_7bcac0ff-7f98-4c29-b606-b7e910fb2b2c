import { BaseMCPServer } from './base-server.js';
import { MCPResponse, MCPNotification, MCPServerConfig } from '../types/mcp.js';
export declare class SSEMCPServer extends BaseMCPServer {
    private app;
    private server?;
    private clients;
    private isRunning;
    private pingInterval?;
    constructor(config: MCPServerConfig);
    private setupMiddleware;
    private setupRoutes;
    private handleSSEConnection;
    start(): Promise<void>;
    stop(): Promise<void>;
    sendResponse(response: MCPResponse): Promise<void>;
    sendNotification(notification: MCPNotification): Promise<void>;
    private startPingInterval;
    get isServerRunning(): boolean;
    get connectedClients(): number;
}
//# sourceMappingURL=sse-server.d.ts.map