{"name": "weapp-performance-optimization-mcp", "version": "1.0.2", "description": "MCP server for WeApp performance optimization analysis", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"weapp-performance-mcp": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "dev:test": "tsx test/index.ts", "start": "node dist/index.js", "test": "node test-example.js && node test-sse.js", "test:stdio": "node test-example.js", "test:sse": "node test-sse.js", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "***********************:xujiazheng/weapp-performance-optimization-mcp.git"}, "keywords": ["mcp", "performance", "weapp", "miniprogram", "optimization"], "author": "", "license": "ISC", "dependencies": {"@types/node": "^20.17.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "json2md": "^1.12.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "resolutions": {"esbuild": "0.25.1"}}