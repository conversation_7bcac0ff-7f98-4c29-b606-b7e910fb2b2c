# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "http://registry.npm.qima-inc.com/@cspotcode/source-map-support/download/@cspotcode/source-map-support-0.8.1.tgz#00629c35a688e05a88b1cda684fb9d5e73f000a1"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@esbuild/aix-ppc64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.1.tgz#c33cf6bbee34975626b01b80451cbb72b4c6c91d"
  integrity sha1-wzz2u+40l1YmsBuARRy7crTGyR0=

"@esbuild/android-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.1.tgz#ea766015c7d2655164f22100d33d7f0308a28d6d"
  integrity sha1-6nZgFcfSZVFk8iEA0z1/AwiijW0=

"@esbuild/android-arm@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.1.tgz#e84d2bf2fe2e6177a0facda3a575b2139fd3cb9c"
  integrity sha1-6E0r8v4uYXeg+s2jpXWyE5/Ty5w=

"@esbuild/android-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.1.tgz#58337bee3bc6d78d10425e5500bd11370cfdfbed"
  integrity sha1-WDN77jvG140QQl5VAL0RNwz9++0=

"@esbuild/darwin-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.1.tgz#a46805c1c585d451aa83be72500bd6e8495dd591"
  integrity sha1-pGgFwcWF1FGqg75yUAvW6Eld1ZE=

"@esbuild/darwin-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.1.tgz#0643e003bb238c63fc93ddbee7d26a003be3cd98"
  integrity sha1-BkPgA7sjjGP8k92+59JqADvjzZg=

"@esbuild/freebsd-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.1.tgz#cff18da5469c09986b93e87979de5d6872fe8f8e"
  integrity sha1-z/GNpUacCZhrk+h5ed5daHL+j44=

"@esbuild/freebsd-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.1.tgz#362fc09c2de14987621c1878af19203c46365dde"
  integrity sha1-Ni/AnC3hSYdiHBh4rxkgPEY2Xd4=

"@esbuild/linux-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.1.tgz#aa90d5b02efc97a271e124e6d1cea490634f7498"
  integrity sha1-qpDVsC78l6Jx4STm0c6kkGNPdJg=

"@esbuild/linux-arm@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.1.tgz#dfcefcbac60a20918b19569b4b657844d39db35a"
  integrity sha1-3878usYKIJGLGVabS2V4RNOds1o=

"@esbuild/linux-ia32@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.1.tgz#6f9527077ccb7953ed2af02e013d4bac69f13754"
  integrity sha1-b5UnB3zLeVPtKvAuAT1LrGnxN1Q=

"@esbuild/linux-loong64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.1.tgz#287d2412a5456e5860c2839d42a4b51284d1697c"
  integrity sha1-KH0kEqVFblhgwoOdQqS1EoTRaXw=

"@esbuild/linux-mips64el@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.1.tgz#530574b9e1bc5d20f7a4f44c5f045e26f3783d57"
  integrity sha1-UwV0ueG8XSD3pPRMXwReJvN4PVc=

"@esbuild/linux-ppc64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.1.tgz#5d7e6b283a0b321ea42c6bc0abeb9eb99c1f5589"
  integrity sha1-XX5rKDoLMh6kLGvAq+ueuZwfVYk=

"@esbuild/linux-riscv64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.1.tgz#14fa0cd073c26b4ee2465d18cd1e18eea7859fa8"
  integrity sha1-FPoM0HPCa07iRl0YzR4Y7qeFn6g=

"@esbuild/linux-s390x@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.1.tgz#e677b4b9d1b384098752266ccaa0d52a420dc1aa"
  integrity sha1-5ne0udGzhAmHUiZsyqDVKkINwao=

"@esbuild/linux-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.1.tgz#f1c796b78fff5ce393658313e8c58613198d9954"
  integrity sha1-8ceWt4//XOOTZYMT6MWGExmNmVQ=

"@esbuild/netbsd-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.1.tgz#0d280b7dfe3973f111b02d5fe9f3063b92796d29"
  integrity sha1-DSgLff45c/ERsC1f6fMGO5J5bSk=

"@esbuild/netbsd-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.1.tgz#be663893931a4bb3f3a009c5cc24fa9681cc71c0"
  integrity sha1-vmY4k5MaS7PzoAnFzCT6loHMccA=

"@esbuild/openbsd-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.1.tgz#d9021b884233673a05dc1cc26de0bf325d824217"
  integrity sha1-2QIbiEIzZzoF3BzCbeC/Ml2CQhc=

"@esbuild/openbsd-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.1.tgz#9f1dc1786ed2e2938c404b06bcc48be9a13250de"
  integrity sha1-nx3BeG7S4pOMQEsGvMSL6aEyUN4=

"@esbuild/sunos-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.1.tgz#89aac24a4b4115959b3f790192cf130396696c27"
  integrity sha1-iarCSktBFZWbP3kBks8TA5ZpbCc=

"@esbuild/win32-arm64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.1.tgz#354358647a6ea98ea6d243bf48bdd7a434999582"
  integrity sha1-NUNYZHpuqY6m0kO/SL3XpDSZlYI=

"@esbuild/win32-ia32@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.1.tgz#8cea7340f2647eba951a041dc95651e3908cd4cb"
  integrity sha1-jOpzQPJkfrqVGgQdyVZR45CM1Ms=

"@esbuild/win32-x64@0.25.1":
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.1.tgz#7d79922cb2d88f9048f06393dbf62d2e4accb584"
  integrity sha1-fXmSLLLYj5BI8GOT2/YtLkrMtYQ=

"@jridgewell/resolve-uri@^3.0.3":
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.5.4"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.4.tgz#7358043433b2e5da569aa02cbc4c121da3af27d7"
  integrity sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "http://registry.npm.qima-inc.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.9.tgz#6534fd5933a53ba7cbf3a17615e273a0d1273ff9"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "http://registry.npm.qima-inc.com/@tsconfig/node10/download/@tsconfig/node10-1.0.9.tgz#df4907fc07a886922637b15e02d4cebc4c0021b2"
  integrity sha1-30kH/AeohpImN7FeAtTOvEwAIbI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/@tsconfig/node12/download/@tsconfig/node12-1.0.11.tgz#ee3def1f27d9ed66dac6e46a295cffb0152e058d"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz#e4386316284f00b98435bf40f72f75a09dabf6c1"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/@tsconfig/node16/download/@tsconfig/node16-1.0.4.tgz#0b92dcc0cc1c81f6f306a381f28e31b1a56536e9"
  integrity sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=

"@types/body-parser@*":
  version "1.19.6"
  resolved "http://registry.npm.qima-inc.com/@types/body-parser/download/@types/body-parser-1.19.6.tgz#1859bebb8fd7dac9918a45d54c1971ab8b5af474"
  integrity sha1-GFm+u4/X2smRikXVTBlxq4ta9HQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "http://registry.npm.qima-inc.com/@types/connect/download/@types/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/cors@^2.8.19":
  version "2.8.19"
  resolved "http://registry.npm.qima-inc.com/@types/cors/download/@types/cors-2.8.19.tgz#d93ea2673fd8c9f697367f5eeefc2bbfa94f0342"
  integrity sha1-2T6iZz/YyfaXNn9e7vwrv6lPA0I=
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@^5.0.0":
  version "5.0.6"
  resolved "http://registry.npm.qima-inc.com/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.6.tgz#41fec4ea20e9c7b22f024ab88a95c6bb288f51b8"
  integrity sha1-Qf7E6iDpx7IvAkq4ipXGuyiPUbg=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@^5.0.3":
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/express/download/@types/express-5.0.3.tgz#6c4bc6acddc2e2a587142e1d8be0bce20757e956"
  integrity sha1-bEvGrN3C4qWHFC4di+C84gdX6VY=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/@types/http-errors/download/@types/http-errors-2.0.5.tgz#5b749ab2b16ba113423feb1a64a95dcd30398472"
  integrity sha1-W3SasrFroRNCP+saZKldzTA5hHI=

"@types/mime@^1":
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/@types/mime/download/@types/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/node@*":
  version "24.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-24.1.0.tgz#0993f7dc31ab5cc402d112315b463e383d68a49c"
  integrity sha1-CZP33DGrXMQC0RIxW0Y+OD1opJw=
  dependencies:
    undici-types "~7.8.0"

"@types/node@^20.17.0":
  version "20.19.9"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-20.19.9.tgz#ca9a58193fec361cc6e859d88b52261853f1f0d3"
  integrity sha1-yppYGT/sNhzG6FnYi1ImGFPx8NM=
  dependencies:
    undici-types "~6.21.0"

"@types/qs@*":
  version "6.14.0"
  resolved "http://registry.npm.qima-inc.com/@types/qs/download/@types/qs-6.14.0.tgz#d8b60cecf62f2db0fb68e5e006077b9178b85de5"
  integrity sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=

"@types/range-parser@*":
  version "1.2.7"
  resolved "http://registry.npm.qima-inc.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/send@*":
  version "0.17.5"
  resolved "http://registry.npm.qima-inc.com/@types/send/download/@types/send-0.17.5.tgz#d991d4f2b16f2b1ef497131f00a9114290791e74"
  integrity sha1-2ZHU8rFvKx70lxMfAKkRQpB5HnQ=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.8"
  resolved "http://registry.npm.qima-inc.com/@types/serve-static/download/@types/serve-static-1.15.8.tgz#8180c3fbe4a70e8f00b9f70b9ba7f08f35987877"
  integrity sha1-gYDD++SnDo8AufcLm6fwjzWYeHc=
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

accepts@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/accepts/download/accepts-2.0.0.tgz#bbcf4ba5075467f3f2131eab3cffc73c2f5d7895"
  integrity sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=
  dependencies:
    mime-types "^3.0.0"
    negotiator "^1.0.0"

acorn-walk@^8.1.1:
  version "8.3.4"
  resolved "http://registry.npm.qima-inc.com/acorn-walk/download/acorn-walk-8.3.4.tgz#794dd169c3977edf4ba4ea47583587c5866236b7"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^8.11.0, acorn@^8.4.1:
  version "8.15.0"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-8.15.0.tgz#a360898bc415edaac46c8241f6383975b930b816"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

arg@^4.1.0:
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/arg/download/arg-4.1.3.tgz#269fc7ad5b8e42cb63c896d5666017261c144089"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

body-parser@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/body-parser/download/body-parser-2.2.0.tgz#f7a9656de305249a715b549b7b8fd1ab9dfddcfa"
  integrity sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=
  dependencies:
    bytes "^3.1.2"
    content-type "^1.0.5"
    debug "^4.4.0"
    http-errors "^2.0.0"
    iconv-lite "^0.6.3"
    on-finished "^2.4.1"
    qs "^6.14.0"
    raw-body "^3.0.0"
    type-is "^2.0.0"

bytes@3.1.2, bytes@^3.1.2:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bound@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/call-bound/download/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

content-disposition@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/content-disposition/download/content-disposition-1.0.0.tgz#844426cb398f934caefcbb172200126bc7ceace2"
  integrity sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

cookie-signature@^1.2.1:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/cookie-signature/download/cookie-signature-1.2.2.tgz#57c7fc3cc293acab9fec54d73e15690ebe4a1793"
  integrity sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=

cookie@^0.7.1:
  version "0.7.2"
  resolved "http://registry.npm.qima-inc.com/cookie/download/cookie-0.7.2.tgz#556369c472a2ba910f2979891b526b3436237ed7"
  integrity sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=

cors@^2.8.5:
  version "2.8.5"
  resolved "http://registry.npm.qima-inc.com/cors/download/cors-2.8.5.tgz#eac11da51592dd86b9f06f6e7ac293b3df875d29"
  integrity sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=
  dependencies:
    object-assign "^4"
    vary "^1"

create-require@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/create-require/download/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

debug@^4.3.5, debug@^4.4.0:
  version "4.4.1"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

depd@2.0.0, depd@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

diff@^4.0.1:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/diff/download/diff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/dunder-proto/download/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

encodeurl@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/encodeurl/download/encodeurl-2.0.0.tgz#7b8ea898077d7e409d3ac45474ea38eaf0857a58"
  integrity sha1-e46omAd9fkCdOsRUdOo46vCFelg=

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/es-define-property/download/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

esbuild@0.25.1, esbuild@~0.25.0:
  version "0.25.1"
  resolved "http://registry.npm.qima-inc.com/esbuild/download/esbuild-0.25.1.tgz#a16b8d070b6ad4871935277bda6ccfe852e3fa2f"
  integrity sha1-oWuNBwtq1IcZNSd72mzP6FLj+i8=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.1"
    "@esbuild/android-arm" "0.25.1"
    "@esbuild/android-arm64" "0.25.1"
    "@esbuild/android-x64" "0.25.1"
    "@esbuild/darwin-arm64" "0.25.1"
    "@esbuild/darwin-x64" "0.25.1"
    "@esbuild/freebsd-arm64" "0.25.1"
    "@esbuild/freebsd-x64" "0.25.1"
    "@esbuild/linux-arm" "0.25.1"
    "@esbuild/linux-arm64" "0.25.1"
    "@esbuild/linux-ia32" "0.25.1"
    "@esbuild/linux-loong64" "0.25.1"
    "@esbuild/linux-mips64el" "0.25.1"
    "@esbuild/linux-ppc64" "0.25.1"
    "@esbuild/linux-riscv64" "0.25.1"
    "@esbuild/linux-s390x" "0.25.1"
    "@esbuild/linux-x64" "0.25.1"
    "@esbuild/netbsd-arm64" "0.25.1"
    "@esbuild/netbsd-x64" "0.25.1"
    "@esbuild/openbsd-arm64" "0.25.1"
    "@esbuild/openbsd-x64" "0.25.1"
    "@esbuild/sunos-x64" "0.25.1"
    "@esbuild/win32-arm64" "0.25.1"
    "@esbuild/win32-ia32" "0.25.1"
    "@esbuild/win32-x64" "0.25.1"

escape-html@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

etag@^1.8.1:
  version "1.8.1"
  resolved "http://registry.npm.qima-inc.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

express@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/express/download/express-5.1.0.tgz#d31beaf715a0016f0d53f47d3b4d7acf28c75cc9"
  integrity sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=
  dependencies:
    accepts "^2.0.0"
    body-parser "^2.2.0"
    content-disposition "^1.0.0"
    content-type "^1.0.5"
    cookie "^0.7.1"
    cookie-signature "^1.2.1"
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    finalhandler "^2.1.0"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    merge-descriptors "^2.0.0"
    mime-types "^3.0.0"
    on-finished "^2.4.1"
    once "^1.4.0"
    parseurl "^1.3.3"
    proxy-addr "^2.0.7"
    qs "^6.14.0"
    range-parser "^1.2.1"
    router "^2.2.0"
    send "^1.1.0"
    serve-static "^2.2.0"
    statuses "^2.0.1"
    type-is "^2.0.1"
    vary "^1.1.2"

finalhandler@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/finalhandler/download/finalhandler-2.1.0.tgz#72306373aa89d05a8242ed569ed86a1bff7c561f"
  integrity sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=
  dependencies:
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    on-finished "^2.4.1"
    parseurl "^1.3.3"
    statuses "^2.0.1"

forwarded@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/fresh/download/fresh-2.0.0.tgz#8dd7df6a1b3a1b3a5cf186c05a5dd267622635a4"
  integrity sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=

fsevents@~2.3.3:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

get-intrinsic@^1.2.5, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/get-proto/download/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-tsconfig@^4.7.5:
  version "4.10.1"
  resolved "http://registry.npm.qima-inc.com/get-tsconfig/download/get-tsconfig-4.10.1.tgz#d34c1c01f47d65a606c37aa7a177bc3e56ab4b2e"
  integrity sha1-00wcAfR9ZaYGw3qnoXe8PlarSy4=
  dependencies:
    resolve-pkg-maps "^1.0.0"

gopd@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/gopd/download/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/has-symbols/download/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

hasown@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

http-errors@2.0.0, http-errors@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

iconv-lite@0.6.3, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

indento@^1.1.13:
  version "1.1.13"
  resolved "http://registry.npm.qima-inc.com/indento/download/indento-1.1.13.tgz#751331b327c04740eeb7be40c5606e6e255c9e36"
  integrity sha1-dRMxsyfAR0Dut75AxWBubiVcnjY=

inherits@2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "http://registry.npm.qima-inc.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-promise@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/is-promise/download/is-promise-4.0.0.tgz#42ff9f84206c1991d26debf520dd5c01042dd2f3"
  integrity sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=

json2md@^1.12.0:
  version "1.12.0"
  resolved "http://registry.npm.qima-inc.com/json2md/download/json2md-1.12.0.tgz#3ac0a2f8f5af140d6f29d91ab1107ca696305165"
  integrity sha1-OsCi+PWvFA1vKdkasRB8ppYwUWU=
  dependencies:
    indento "^1.1.13"

make-error@^1.1.1:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

media-typer@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/media-typer/download/media-typer-1.1.0.tgz#6ab74b8f2d3320f2064b2a87a38e7931ff3a5561"
  integrity sha1-ardLjy0zIPIGSyqHo455Mf86VWE=

merge-descriptors@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/merge-descriptors/download/merge-descriptors-2.0.0.tgz#ea922f660635a2249ee565e0449f951e6b603808"
  integrity sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=

mime-db@^1.54.0:
  version "1.54.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.54.0.tgz#cddb3ee4f9c64530dff640236661d42cb6a314f5"
  integrity sha1-zds+5PnGRTDf9kAjZmHULLajFPU=

mime-types@^3.0.0, mime-types@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-3.0.1.tgz#b1d94d6997a9b32fd69ebaed0db73de8acb519ce"
  integrity sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=
  dependencies:
    mime-db "^1.54.0"

ms@^2.1.3:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

negotiator@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/negotiator/download/negotiator-1.0.0.tgz#b6c91bb47172d69f93cfd7c357bbb529019b5f6a"
  integrity sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=

object-assign@^4:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "http://registry.npm.qima-inc.com/object-inspect/download/object-inspect-1.13.4.tgz#8375265e21bc20d0fa582c22e1b13485d6e00213"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

on-finished@^2.4.1:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

parseurl@^1.3.3:
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

path-to-regexp@^8.0.0:
  version "8.2.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-8.2.0.tgz#73990cc29e57a3ff2a0d914095156df5db79e8b4"
  integrity sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=

proxy-addr@^2.0.7:
  version "2.0.7"
  resolved "http://registry.npm.qima-inc.com/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

qs@^6.14.0:
  version "6.14.0"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.14.0.tgz#c63fa40680d2c5c941412a0e899c89af60c0a930"
  integrity sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=
  dependencies:
    side-channel "^1.1.0"

range-parser@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/raw-body/download/raw-body-3.0.0.tgz#25b3476f07a51600619dae3fe82ddc28a36e5e0f"
  integrity sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.6.3"
    unpipe "1.0.0"

resolve-pkg-maps@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-pkg-maps/download/resolve-pkg-maps-1.0.0.tgz#616b3dc2c57056b5588c31cdf4b3d64db133720f"
  integrity sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=

router@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/router/download/router-2.2.0.tgz#019be620b711c87641167cc79b99090f00b146ef"
  integrity sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=
  dependencies:
    debug "^4.4.0"
    depd "^2.0.0"
    is-promise "^4.0.0"
    parseurl "^1.3.3"
    path-to-regexp "^8.0.0"

safe-buffer@5.2.1:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

send@^1.1.0, send@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/send/download/send-1.2.0.tgz#32a7554fb777b831dfa828370f773a3808d37212"
  integrity sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=
  dependencies:
    debug "^4.3.5"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    mime-types "^3.0.1"
    ms "^2.1.3"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    statuses "^2.0.1"

serve-static@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/serve-static/download/serve-static-2.2.0.tgz#9c02564ee259bdd2251b82d659a2e7e1938d66f9"
  integrity sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=
  dependencies:
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    parseurl "^1.3.3"
    send "^1.2.0"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/side-channel-list/download/side-channel-list-1.0.0.tgz#10cb5984263115d3b7a0e336591e290a830af8ad"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/side-channel-map/download/side-channel-map-1.0.1.tgz#d6bb6b37902c6fef5174e5f533fab4c732a26f42"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz#11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/side-channel/download/side-channel-1.1.0.tgz#c3fcff9c4da932784873335ec9765fa94ff66bc9"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

statuses@2.0.1, statuses@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

ts-node@^10.9.2:
  version "10.9.2"
  resolved "http://registry.npm.qima-inc.com/ts-node/download/ts-node-10.9.2.tgz#70f021c9e185bccdca820e26dc413805c101c71f"
  integrity sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsx@^4.19.2:
  version "4.20.3"
  resolved "http://registry.npm.qima-inc.com/tsx/download/tsx-4.20.3.tgz#f913e4911d59ad177c1bcee19d1035ef8dd6e2fb"
  integrity sha1-+RPkkR1ZrRd8G87hnRA1743W4vs=
  dependencies:
    esbuild "~0.25.0"
    get-tsconfig "^4.7.5"
  optionalDependencies:
    fsevents "~2.3.3"

type-is@^2.0.0, type-is@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/type-is/download/type-is-2.0.1.tgz#64f6cf03f92fce4015c2b224793f6bdd4b068c97"
  integrity sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=
  dependencies:
    content-type "^1.0.5"
    media-typer "^1.1.0"
    mime-types "^3.0.0"

typescript@^5.7.3:
  version "5.8.3"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-5.8.3.tgz#92f8a3e5e3cf497356f4178c34cd65a7f5e8440e"
  integrity sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=

undici-types@~6.21.0:
  version "6.21.0"
  resolved "http://registry.npm.qima-inc.com/undici-types/download/undici-types-6.21.0.tgz#691d00af3909be93a7faa13be61b3a5b50ef12cb"
  integrity sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=

undici-types@~7.8.0:
  version "7.8.0"
  resolved "http://registry.npm.qima-inc.com/undici-types/download/undici-types-7.8.0.tgz#de00b85b710c54122e44fbfd911f8d70174cd294"
  integrity sha1-3gC4W3EMVBIuRPv9kR+NcBdM0pQ=

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/v8-compile-cache-lib/download/v8-compile-cache-lib-3.0.1.tgz#6336e8d71965cb3d35a1bbb7868445a7c05264bf"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

vary@^1, vary@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

yn@3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/yn/download/yn-3.1.1.tgz#1e87401a09d767c1d5eab26a6e4c185182d2eb50"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=
