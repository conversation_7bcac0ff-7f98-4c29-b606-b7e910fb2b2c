# WeApp Performance Optimization MCP Server

A Model Context Protocol (MCP) server that provides performance analysis tools for WeChat Mini Programs (WeApp). This server supports both stdio and Server-Sent Events (SSE) communication methods.

## Features

- **Performance Analysis**: Analyze WeApp code for performance issues and optimization opportunities
- **Multiple Communication Methods**: Support for both stdio and SSE communication
- **Comprehensive Analysis**: Covers JavaScript, WXML, WXSS, and JSON configuration files
- **Detailed Reporting**: Provides severity levels, impact analysis, and actionable recommendations

## Installation

### From npm (when published)
```bash
npm install -g weapp-performance-optimization-mcp
```

### From source
```bash
git clone <repository-url>
cd weapp-performance-optimization-mcp
npm install
npm run build
npm link
```

## Usage

### Command Line Interface

```bash
# Start with stdio communication (default)
weapp-performance-mcp

# Start with SSE communication on default port (3000)
weapp-performance-mcp --method sse

# Start with SSE communication on custom port
weapp-performance-mcp --method sse --port 8080

# Show help
weapp-performance-mcp --help
```

### Communication Methods

#### 1. Stdio Communication
Best for direct integration with other tools or scripts.

```bash
weapp-performance-mcp --method stdio
```

#### 2. Server-Sent Events (SSE)
Best for web-based clients or HTTP-based integrations.

```bash
weapp-performance-mcp --method sse --port 3000
```

When using SSE mode, the following endpoints are available:
- `GET /events` - SSE endpoint for real-time communication
- `POST /request` - HTTP endpoint for sending MCP requests
- `GET /health` - Health check endpoint

## Tools

### analyze_performance_issues

Analyzes WeApp code for performance issues and provides optimization recommendations.

#### Parameters

- `code` (string, required): The WeApp code to analyze
- `fileType` (string, required): The type of file being analyzed (`js`, `wxml`, `wxss`, `json`)
- `context` (object, optional): Additional context information
  - `pagePath` (string): The page path if analyzing a page file
  - `componentName` (string): The component name if analyzing a component
  - `isMainPackage` (boolean): Whether this file is in the main package

#### Example Request (JSON-RPC 2.0)

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "analyze_performance_issues",
    "arguments": {
      "code": "Page({\n  data: {\n    items: []\n  },\n  onLoad() {\n    for(let i = 0; i < 100; i++) {\n      this.setData({\n        [`items[${i}]`]: i\n      });\n    }\n  }\n});",
      "fileType": "js",
      "context": {
        "pagePath": "pages/index/index",
        "isMainPackage": true
      }
    }
  }
}
```

#### Example Response

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "# WeApp Performance Analysis Report\n\n## Summary\n- Total Issues: 2\n- Critical: 0\n- High: 1\n- Medium: 0\n- Low: 1\n\n## Issues Found\n\n### 1. setData called inside loop\n**Type:** cpu | **Severity:** high\n\n**Impact:** Multiple setData calls in loops can severely impact performance\n\n**Suggestion:** Collect all data changes and call setData once after the loop\n\n---\n\n## General Recommendations\n\n1. Optimize rendering by reducing setData frequency and using efficient list rendering\n"
      }
    ]
  }
}
```

## Analysis Types

The tool analyzes different types of performance issues:

### JavaScript Analysis
- Synchronous API calls that block the main thread
- Excessive setData calls
- Large data objects in setData
- Timer leaks
- Inefficient loops with setData
- Missing error handling in network requests

### WXML Analysis
- Excessive wx:for loops
- Missing wx:key attributes
- Complex template expressions
- Deep nesting levels

### WXSS Analysis
- Expensive CSS properties
- Excessive use of !important
- Universal selectors

### JSON Configuration Analysis
- Excessive subpackages
- Missing preload rules
- Excessive permissions
- Invalid JSON syntax

## Development

### Building

```bash
npm run build
```

### Development Mode

```bash
npm run dev
```

### Testing

```bash
npm test
```

## API Reference

### MCP Protocol Support

This server implements the Model Context Protocol (MCP) specification and supports:

- `initialize` - Initialize the MCP session
- `tools/list` - List available tools
- `tools/call` - Execute a tool

### Server Capabilities

```json
{
  "tools": {
    "listChanged": false
  }
}
```

## Examples

### Using with stdio

```bash
echo '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | weapp-performance-mcp
```

### Using with SSE

```bash
# Start server
weapp-performance-mcp --method sse --port 3000

# In another terminal, send request
curl -X POST http://localhost:3000/request \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

ISC

## Support

For issues and questions, please open an issue on the GitHub repository.