# Usage Examples

## Example 1: Analyzing JavaScript Code

### Input Code (problematic)
```javascript
Page({
  data: {
    items: [],
    loading: false
  },
  
  onLoad() {
    // Problem 1: Multiple setData calls in loop
    for(let i = 0; i < 100; i++) {
      this.setData({
        [`items[${i}]`]: {
          id: i,
          name: `Item ${i}`
        }
      });
    }
    
    // Problem 2: Synchronous API call
    const systemInfo = wx.getSystemInfoSync();
    
    // Problem 3: Timer without cleanup
    setInterval(() => {
      this.loadData();
    }, 5000);
  },
  
  loadData() {
    // Problem 4: Network request without error handling
    wx.request({
      url: 'https://api.example.com/data',
      success: (res) => {
        this.setData({
          items: res.data
        });
      }
    });
  }
});
```

### MCP Request
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "analyze_performance_issues",
    "arguments": {
      "code": "Page({\n  data: {\n    items: [],\n    loading: false\n  },\n  \n  onLoad() {\n    // Problem 1: Multiple setData calls in loop\n    for(let i = 0; i < 100; i++) {\n      this.setData({\n        [`items[${i}]`]: {\n          id: i,\n          name: `Item ${i}`\n        }\n      });\n    }\n    \n    // Problem 2: Synchronous API call\n    const systemInfo = wx.getSystemInfoSync();\n    \n    // Problem 3: Timer without cleanup\n    setInterval(() => {\n      this.loadData();\n    }, 5000);\n  },\n  \n  loadData() {\n    // Problem 4: Network request without error handling\n    wx.request({\n      url: 'https://api.example.com/data',\n      success: (res) => {\n        this.setData({\n          items: res.data\n        });\n      }\n    });\n  }\n});",
      "fileType": "js",
      "context": {
        "pagePath": "pages/list/list",
        "isMainPackage": true
      }
    }
  }
}
```

### Expected Analysis Result
The tool will identify multiple performance issues:
- High severity: setData called inside loop
- Medium severity: Synchronous API calls detected
- High severity: Potential timer leak detected
- Medium severity: Network requests without error handling

## Example 2: Analyzing WXML Template

### Input Code (problematic)
```xml
<view class="container">
  <view wx:for="{{items}}" wx:for-item="item" class="item">
    <text>{{item.name ? item.name : 'No name'}}</text>
    <text>{{item.price ? '¥' + item.price : 'Free'}}</text>
    <view wx:if="{{item.tags && item.tags.length > 0}}">
      <text wx:for="{{item.tags}}" wx:for-item="tag">{{tag}}</text>
    </view>
  </view>
</view>
```

### Issues Found
- Missing wx:key for list rendering optimization
- Complex expressions in template that should be moved to JavaScript
- Nested wx:for without proper keys

## Example 3: Analyzing WXSS Styles

### Input Code (problematic)
```css
* {
  box-sizing: border-box !important;
}

.container {
  box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
  border-radius: 10px !important;
  transform: translateZ(0) !important;
}

.item {
  filter: blur(0.5px) !important;
  opacity: 0.9 !important;
}
```

### Issues Found
- Universal selector (*) usage
- Excessive use of !important
- Multiple expensive CSS properties (box-shadow, border-radius, transform, filter, opacity)

## Example 4: Analyzing JSON Configuration

### Input Code (problematic)
```json
{
  "pages": [
    "pages/index/index",
    "pages/list/list"
  ],
  "subpackages": [
    {"root": "pages/user", "pages": ["profile", "settings", "history"]},
    {"root": "pages/shop", "pages": ["products", "cart", "checkout"]},
    {"root": "pages/admin", "pages": ["dashboard", "users", "reports"]},
    {"root": "pages/help", "pages": ["faq", "contact", "feedback"]},
    {"root": "pages/game", "pages": ["lobby", "play", "scores"]},
    {"root": "pages/social", "pages": ["friends", "chat", "groups"]},
    {"root": "pages/tools", "pages": ["calculator", "converter", "timer"]},
    {"root": "pages/news", "pages": ["list", "detail", "category"]},
    {"root": "pages/media", "pages": ["photos", "videos", "music"]}
  ],
  "permission": {
    "scope.userLocation": {"desc": "Location access"},
    "scope.camera": {"desc": "Camera access"},
    "scope.record": {"desc": "Microphone access"},
    "scope.writePhotosAlbum": {"desc": "Photo album access"},
    "scope.userInfo": {"desc": "User info access"},
    "scope.address": {"desc": "Address access"}
  }
}
```

### Issues Found
- Too many subpackages (9, recommended < 8)
- Subpackages without preload rules
- Many permissions requested (6, consider reducing)

## Running the Examples

### Using stdio mode:
```bash
echo '{"jsonrpc":"2.0","id":1,"method":"tools/call","params":{"name":"analyze_performance_issues","arguments":{"code":"...","fileType":"js"}}}' | weapp-performance-mcp
```

### Using SSE mode:
```bash
# Start server
weapp-performance-mcp --method sse --port 3000

# Send request
curl -X POST http://localhost:3000/request \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/call","params":{"name":"analyze_performance_issues","arguments":{"code":"...","fileType":"js"}}}'
```
