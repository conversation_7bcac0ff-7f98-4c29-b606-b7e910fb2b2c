#!/usr/bin/env node

// Simple test script to verify the MCP server works
const { spawn } = require('child_process');

async function testStdioMode() {
  console.log('Testing stdio mode...');
  
  const server = spawn('node', ['dist/index.js', '--method', 'stdio'], {
    stdio: ['pipe', 'pipe', 'inherit']
  });

  // Test initialize request
  const initRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  };

  // Test tools/list request
  const toolsListRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };

  // Test analyze_performance_issues tool
  const analyzeRequest = {
    jsonrpc: '2.0',
    id: 3,
    method: 'tools/call',
    params: {
      name: 'analyze_performance_issues',
      arguments: {
        code: `Page({
  data: {
    items: []
  },
  onLoad() {
    for(let i = 0; i < 100; i++) {
      this.setData({
        [\`items[\${i}]\`]: i
      });
    }
  }
});`,
        fileType: 'js',
        context: {
          pagePath: 'pages/index/index',
          isMainPackage: true
        }
      }
    }
  };

  let responseCount = 0;
  let responses = [];

  server.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        try {
          const response = JSON.parse(line);
          responses.push(response);
          responseCount++;
          console.log(`Response ${responseCount}:`, JSON.stringify(response, null, 2));
          
          if (responseCount === 3) {
            server.kill();
            console.log('\nTest completed successfully!');
            process.exit(0);
          }
        } catch (error) {
          console.error('Error parsing response:', error);
        }
      }
    });
  });

  server.on('error', (error) => {
    console.error('Server error:', error);
    process.exit(1);
  });

  // Send requests
  setTimeout(() => {
    server.stdin.write(JSON.stringify(initRequest) + '\n');
  }, 100);

  setTimeout(() => {
    server.stdin.write(JSON.stringify(toolsListRequest) + '\n');
  }, 200);

  setTimeout(() => {
    server.stdin.write(JSON.stringify(analyzeRequest) + '\n');
  }, 300);

  // Timeout after 10 seconds
  setTimeout(() => {
    console.error('Test timed out');
    server.kill();
    process.exit(1);
  }, 10000);
}

testStdioMode().catch(console.error);
